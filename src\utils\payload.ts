// Payload Processing Utilities
// Functions for handling and parsing various data formats

/**
 * Format and parse payload data from various sources
 */
export const formatPayload = (payload: unknown): unknown | null => {
  try {
    if (payload instanceof Buffer || payload instanceof ArrayBuffer) {
      const buffer = payload as Buffer
      const jsonStr = buffer.toString('utf-8')
      const jsonData = JSON.parse(jsonStr)
      return jsonData
    }

    if (typeof payload === 'string') {
      try {
        return JSON.parse(payload)
      } catch {
        return payload // Return as string if not valid JSON
      }
    }

    return payload
  } catch (error) {
    console.error('Error parsing payload:', error)
    return null
  }
}

/**
 * Validate payload structure
 */
export const validatePayload = (payload: unknown, expectedKeys: string[]): boolean => {
  if (!payload || typeof payload !== 'object') return false

  const payloadObj = payload as Record<string, unknown>
  return expectedKeys.every((key) => key in payloadObj)
}

/**
 * Sanitize payload data
 */
export const sanitizePayload = (payload: unknown): unknown => {
  if (payload === null || payload === undefined) return null

  if (typeof payload === 'string') {
    return payload.trim()
  }

  if (Array.isArray(payload)) {
    return payload.map((item) => sanitizePayload(item))
  }

  if (typeof payload === 'object') {
    const sanitized: Record<string, unknown> = {}
    for (const [key, value] of Object.entries(payload)) {
      sanitized[key] = sanitizePayload(value)
    }
    return sanitized
  }

  return payload
}
