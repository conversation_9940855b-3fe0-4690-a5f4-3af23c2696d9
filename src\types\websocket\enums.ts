export enum AccountType {
  Demo = 1,
  Live = 0
}

export enum WSConnectionState {
  DISCONNECTED = 'disconnected',
  CONNECTING = 'connecting',
  CONNECTED = 'connected',
  RECONNECTING = 'reconnecting',
  ERROR = 'error'
}

export enum WSLogLevel {
  DEBUG = 'DEBUG',
  INFO = 'INFO',
  WARN = 'WARN',
  ERROR = 'ERROR'
}

export enum WSErrorCode {
  CONNECTION_SETUP_FAILED = 'CONNECTION_SETUP_FAILED',
  SOCKET_NOT_AVAILABLE = 'SOCKET_NOT_AVAILABLE',
  MESSAGE_SEND_FAILED = 'MESSAGE_SEND_FAILED',
  MAX_RECONNECT_ATTEMPTS_EXCEEDED = 'MAX_RECONNECT_ATTEMPTS_EXCEEDED',
  AUTHENTICATION_FAILED = 'AUTHENTICATION_FAILED',
  HEARTBEAT_TIMEOUT = 'HEARTBEAT_TIMEOUT',
  EVENT_PROCESSING_ERROR = 'EVENT_PROCESSING_ERROR'
}
