// Trading Calculation Utilities
// Mathematical functions for trading analysis and risk management

import type { TradeHistory } from '../../types/trading'

/**
 * Calculate position size based on risk percentage
 */
export const calculatePositionSize = (
  accountBalance: number,
  riskPercentage: number,
  entryPrice: number,
  stopLoss: number
): number => {
  const riskAmount = accountBalance * (riskPercentage / 100)
  const priceRisk = Math.abs(entryPrice - stopLoss)
  return riskAmount / priceRisk
}

/**
 * Calculate profit/loss for a binary option
 */
export const calculateBinaryOptionPnL = (
  amount: number,
  payoutPercentage: number,
  isWin: boolean
): number => {
  if (isWin) {
    return amount * (payoutPercentage / 100)
  }
  return -amount
}

/**
 * Calculate win rate from trade history
 */
export const calculateWinRate = (trades: TradeHistory[]): number => {
  if (trades.length === 0) return 0
  const winningTrades = trades.filter((trade) => trade.profit > 0).length
  return (winningTrades / trades.length) * 100
}

/**
 * Calculate profit factor
 */
export const calculateProfitFactor = (trades: TradeHistory[]): number => {
  const grossProfit = trades
    .filter((trade) => trade.profit > 0)
    .reduce((sum, trade) => sum + trade.profit, 0)

  const grossLoss = Math.abs(
    trades.filter((trade) => trade.profit < 0).reduce((sum, trade) => sum + trade.profit, 0)
  )

  return grossLoss === 0 ? grossProfit : grossProfit / grossLoss
}

/**
 * Calculate maximum drawdown
 */
export const calculateMaxDrawdown = (equityCurve: number[]): number => {
  let maxDrawdown = 0
  let peak = equityCurve[0]

  for (const equity of equityCurve) {
    if (equity > peak) {
      peak = equity
    }

    const drawdown = ((peak - equity) / peak) * 100
    if (drawdown > maxDrawdown) {
      maxDrawdown = drawdown
    }
  }

  return maxDrawdown
}

/**
 * Calculate Sharpe ratio
 */
export const calculateSharpeRatio = (returns: number[], riskFreeRate: number = 0.02): number => {
  if (returns.length === 0) return 0

  const avgReturn = returns.reduce((sum, ret) => sum + ret, 0) / returns.length
  const variance =
    returns.reduce((sum, ret) => sum + Math.pow(ret - avgReturn, 2), 0) / returns.length
  const stdDev = Math.sqrt(variance)

  return stdDev === 0 ? 0 : (avgReturn - riskFreeRate) / stdDev
}

/**
 * Calculate Kelly Criterion for optimal position sizing
 */
export const calculateKellyCriterion = (
  winRate: number,
  averageWin: number,
  averageLoss: number
): number => {
  const winProbability = winRate / 100
  const lossProbability = 1 - winProbability
  const winLossRatio = averageWin / Math.abs(averageLoss)

  return (winProbability * winLossRatio - lossProbability) / winLossRatio
}

/**
 * Calculate compound annual growth rate (CAGR)
 */
export const calculateCAGR = (initialValue: number, finalValue: number, years: number): number => {
  return (Math.pow(finalValue / initialValue, 1 / years) - 1) * 100
}

/**
 * Calculate volatility (standard deviation of returns)
 */
export const calculateVolatility = (prices: number[]): number => {
  if (prices.length < 2) return 0

  const returns = []
  for (let i = 1; i < prices.length; i++) {
    returns.push((prices[i] - prices[i - 1]) / prices[i - 1])
  }

  const avgReturn = returns.reduce((sum, ret) => sum + ret, 0) / returns.length
  const variance =
    returns.reduce((sum, ret) => sum + Math.pow(ret - avgReturn, 2), 0) / returns.length

  return Math.sqrt(variance) * Math.sqrt(252) * 100 // Annualized volatility
}

/**
 * Calculate correlation between two price series
 */
export const calculateCorrelation = (prices1: number[], prices2: number[]): number => {
  if (prices1.length !== prices2.length || prices1.length < 2) return 0

  const n = prices1.length
  const sum1 = prices1.reduce((sum, price) => sum + price, 0)
  const sum2 = prices2.reduce((sum, price) => sum + price, 0)
  const sum1Sq = prices1.reduce((sum, price) => sum + price * price, 0)
  const sum2Sq = prices2.reduce((sum, price) => sum + price * price, 0)
  const sumProducts = prices1.reduce((sum, price, i) => sum + price * prices2[i], 0)

  const numerator = n * sumProducts - sum1 * sum2
  const denominator = Math.sqrt((n * sum1Sq - sum1 * sum1) * (n * sum2Sq - sum2 * sum2))

  return denominator === 0 ? 0 : numerator / denominator
}

/**
 * Calculate beta (systematic risk) relative to market
 */
export const calculateBeta = (assetReturns: number[], marketReturns: number[]): number => {
  if (assetReturns.length !== marketReturns.length || assetReturns.length < 2) return 1

  const correlation = calculateCorrelation(assetReturns, marketReturns)
  const assetVolatility = calculateVolatility(assetReturns) / 100
  const marketVolatility = calculateVolatility(marketReturns) / 100

  return marketVolatility === 0 ? 1 : correlation * (assetVolatility / marketVolatility)
}

/**
 * Calculate Value at Risk (VaR) using historical method
 */
export const calculateVaR = (returns: number[], confidenceLevel: number = 0.95): number => {
  if (returns.length === 0) return 0

  const sortedReturns = [...returns].sort((a, b) => a - b)
  const index = Math.floor((1 - confidenceLevel) * sortedReturns.length)

  return Math.abs(sortedReturns[index] || 0)
}

/**
 * Calculate Expected Shortfall (Conditional VaR)
 */
export const calculateExpectedShortfall = (
  returns: number[],
  confidenceLevel: number = 0.95
): number => {
  if (returns.length === 0) return 0

  const sortedReturns = [...returns].sort((a, b) => a - b)
  const cutoffIndex = Math.floor((1 - confidenceLevel) * sortedReturns.length)
  const tailReturns = sortedReturns.slice(0, cutoffIndex + 1)

  if (tailReturns.length === 0) return 0

  const avgTailReturn = tailReturns.reduce((sum, ret) => sum + ret, 0) / tailReturns.length
  return Math.abs(avgTailReturn)
}

/**
 * Calculate optimal portfolio weights using mean-variance optimization
 */
export const calculateOptimalWeights = (
  expectedReturns: number[],
  covarianceMatrix: number[][],
  riskTolerance: number = 1
): number[] => {
  // Simplified implementation - in practice, use a proper optimization library
  const n = expectedReturns.length
  const weights = new Array(n).fill(1 / n) // Equal weights as starting point

  // This is a placeholder for a more sophisticated optimization algorithm
  // In a real implementation, you would use quadratic programming

  return weights
}

/**
 * Calculate information ratio
 */
export const calculateInformationRatio = (
  portfolioReturns: number[],
  benchmarkReturns: number[]
): number => {
  if (portfolioReturns.length !== benchmarkReturns.length || portfolioReturns.length === 0) return 0

  const excessReturns = portfolioReturns.map((ret, i) => ret - benchmarkReturns[i])
  const avgExcessReturn = excessReturns.reduce((sum, ret) => sum + ret, 0) / excessReturns.length

  const trackingError = Math.sqrt(
    excessReturns.reduce((sum, ret) => sum + Math.pow(ret - avgExcessReturn, 2), 0) /
      excessReturns.length
  )

  return trackingError === 0 ? 0 : avgExcessReturn / trackingError
}

/**
 * Calculate Sortino ratio (downside deviation)
 */
export const calculateSortinoRatio = (returns: number[], targetReturn: number = 0): number => {
  if (returns.length === 0) return 0

  const avgReturn = returns.reduce((sum, ret) => sum + ret, 0) / returns.length
  const downsideReturns = returns.filter((ret) => ret < targetReturn)

  if (downsideReturns.length === 0) return Infinity

  const downsideDeviation = Math.sqrt(
    downsideReturns.reduce((sum, ret) => sum + Math.pow(ret - targetReturn, 2), 0) /
      downsideReturns.length
  )

  return downsideDeviation === 0 ? 0 : (avgReturn - targetReturn) / downsideDeviation
}
