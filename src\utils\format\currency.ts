// Currency and Number Formatting Utilities
// Comprehensive formatting functions for financial data

/**
 * Format currency with proper locale and currency code
 */
export const formatCurrency = (
  amount: number,
  currency: string = 'USD',
  locale: string = 'en-US',
  options: Intl.NumberFormatOptions = {}
): string => {
  try {
    return new Intl.NumberFormat(locale, {
      style: 'currency',
      currency,
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
      ...options
    }).format(amount)
  } catch (error) {
    console.warn('Currency formatting error:', error)
    return `${currency} ${amount.toFixed(2)}`
  }
}

/**
 * Format percentage with proper precision
 */
export const formatPercentage = (
  value: number,
  decimals: number = 2,
  showSign: boolean = false
): string => {
  const formatted = (value * 100).toFixed(decimals)
  const sign = showSign && value > 0 ? '+' : ''
  return `${sign}${formatted}%`
}

/**
 * Format large numbers with K, M, B suffixes
 */
export const formatCompactNumber = (
  value: number,
  decimals: number = 1,
  locale: string = 'en-US'
): string => {
  try {
    return new Intl.NumberFormat(locale, {
      notation: 'compact',
      compactDisplay: 'short',
      minimumFractionDigits: 0,
      maximumFractionDigits: decimals
    }).format(value)
  } catch (error) {
    // Fallback for older browsers
    const absValue = Math.abs(value)
    const sign = value < 0 ? '-' : ''
    
    if (absValue >= 1e9) {
      return `${sign}${(absValue / 1e9).toFixed(decimals)}B`
    } else if (absValue >= 1e6) {
      return `${sign}${(absValue / 1e6).toFixed(decimals)}M`
    } else if (absValue >= 1e3) {
      return `${sign}${(absValue / 1e3).toFixed(decimals)}K`
    }
    
    return value.toFixed(decimals)
  }
}

/**
 * Format number with thousands separators
 */
export const formatNumber = (
  value: number,
  decimals: number = 2,
  locale: string = 'en-US'
): string => {
  try {
    return new Intl.NumberFormat(locale, {
      minimumFractionDigits: decimals,
      maximumFractionDigits: decimals
    }).format(value)
  } catch (error) {
    return value.toFixed(decimals).replace(/\B(?=(\d{3})+(?!\d))/g, ',')
  }
}

/**
 * Format profit/loss with color indication
 */
export const formatPnL = (
  value: number,
  currency: string = 'USD',
  locale: string = 'en-US'
): { formatted: string; color: 'green' | 'red' | 'gray'; isPositive: boolean } => {
  const formatted = formatCurrency(Math.abs(value), currency, locale)
  const sign = value > 0 ? '+' : value < 0 ? '-' : ''
  
  return {
    formatted: `${sign}${formatted}`,
    color: value > 0 ? 'green' : value < 0 ? 'red' : 'gray',
    isPositive: value > 0
  }
}

/**
 * Format price with appropriate decimal places based on value
 */
export const formatPrice = (
  price: number,
  symbol?: string,
  minDecimals: number = 2
): string => {
  let decimals = minDecimals
  
  // Adjust decimal places based on price magnitude
  if (price >= 1000) {
    decimals = Math.max(2, minDecimals)
  } else if (price >= 100) {
    decimals = Math.max(3, minDecimals)
  } else if (price >= 10) {
    decimals = Math.max(4, minDecimals)
  } else if (price >= 1) {
    decimals = Math.max(5, minDecimals)
  } else {
    // For very small prices, show more decimals
    decimals = Math.max(6, minDecimals)
  }
  
  const formatted = price.toFixed(decimals)
  return symbol ? `${formatted} ${symbol}` : formatted
}

/**
 * Format volume with appropriate units
 */
export const formatVolume = (
  volume: number,
  decimals: number = 1
): string => {
  if (volume >= 1e9) {
    return `${(volume / 1e9).toFixed(decimals)}B`
  } else if (volume >= 1e6) {
    return `${(volume / 1e6).toFixed(decimals)}M`
  } else if (volume >= 1e3) {
    return `${(volume / 1e3).toFixed(decimals)}K`
  }
  
  return volume.toFixed(0)
}

/**
 * Format market cap
 */
export const formatMarketCap = (
  marketCap: number,
  currency: string = 'USD'
): string => {
  const compact = formatCompactNumber(marketCap, 2)
  return `${compact} ${currency}`
}

/**
 * Format spread (bid-ask difference)
 */
export const formatSpread = (
  bid: number,
  ask: number,
  showBps: boolean = true
): string => {
  const spread = ask - bid
  const spreadPercentage = ((spread / bid) * 100).toFixed(4)
  
  if (showBps) {
    const basisPoints = (spread / bid) * 10000
    return `${spread.toFixed(5)} (${basisPoints.toFixed(1)} bps)`
  }
  
  return `${spread.toFixed(5)} (${spreadPercentage}%)`
}

/**
 * Format time duration
 */
export const formatDuration = (
  milliseconds: number,
  format: 'short' | 'long' = 'short'
): string => {
  const seconds = Math.floor(milliseconds / 1000)
  const minutes = Math.floor(seconds / 60)
  const hours = Math.floor(minutes / 60)
  const days = Math.floor(hours / 24)
  
  if (format === 'short') {
    if (days > 0) return `${days}d ${hours % 24}h`
    if (hours > 0) return `${hours}h ${minutes % 60}m`
    if (minutes > 0) return `${minutes}m ${seconds % 60}s`
    return `${seconds}s`
  } else {
    const parts: string[] = []
    if (days > 0) parts.push(`${days} day${days !== 1 ? 's' : ''}`)
    if (hours % 24 > 0) parts.push(`${hours % 24} hour${hours % 24 !== 1 ? 's' : ''}`)
    if (minutes % 60 > 0) parts.push(`${minutes % 60} minute${minutes % 60 !== 1 ? 's' : ''}`)
    if (seconds % 60 > 0) parts.push(`${seconds % 60} second${seconds % 60 !== 1 ? 's' : ''}`)
    
    return parts.join(', ') || '0 seconds'
  }
}

/**
 * Format risk percentage with warning levels
 */
export const formatRisk = (
  riskPercentage: number
): { formatted: string; level: 'low' | 'medium' | 'high' | 'extreme' } => {
  const formatted = formatPercentage(riskPercentage / 100, 1)
  
  let level: 'low' | 'medium' | 'high' | 'extreme'
  if (riskPercentage <= 2) {
    level = 'low'
  } else if (riskPercentage <= 5) {
    level = 'medium'
  } else if (riskPercentage <= 10) {
    level = 'high'
  } else {
    level = 'extreme'
  }
  
  return { formatted, level }
}

/**
 * Format win rate with visual indicator
 */
export const formatWinRate = (
  winRate: number
): { formatted: string; grade: 'A' | 'B' | 'C' | 'D' | 'F' } => {
  const formatted = formatPercentage(winRate / 100, 1)
  
  let grade: 'A' | 'B' | 'C' | 'D' | 'F'
  if (winRate >= 80) {
    grade = 'A'
  } else if (winRate >= 70) {
    grade = 'B'
  } else if (winRate >= 60) {
    grade = 'C'
  } else if (winRate >= 50) {
    grade = 'D'
  } else {
    grade = 'F'
  }
  
  return { formatted, grade }
}

/**
 * Format leverage ratio
 */
export const formatLeverage = (leverage: number): string => {
  return `${leverage}:1`
}

/**
 * Format margin requirement
 */
export const formatMargin = (
  positionSize: number,
  leverage: number,
  currency: string = 'USD'
): string => {
  const marginRequired = positionSize / leverage
  return formatCurrency(marginRequired, currency)
}

/**
 * Format pip value for forex
 */
export const formatPipValue = (
  pipValue: number,
  currency: string = 'USD'
): string => {
  return formatCurrency(pipValue, currency, 'en-US', { minimumFractionDigits: 2, maximumFractionDigits: 5 })
}

/**
 * Format order size with lot notation
 */
export const formatOrderSize = (
  size: number,
  instrument: 'forex' | 'stocks' | 'crypto' = 'forex'
): string => {
  switch (instrument) {
    case 'forex':
      if (size >= 100000) {
        return `${(size / 100000).toFixed(2)} lots`
      } else if (size >= 10000) {
        return `${(size / 10000).toFixed(2)} mini lots`
      } else {
        return `${(size / 1000).toFixed(2)} micro lots`
      }
    case 'stocks':
      return `${formatNumber(size, 0)} shares`
    case 'crypto':
      return formatNumber(size, 8)
    default:
      return formatNumber(size, 2)
  }
}

/**
 * Format account balance with equity information
 */
export const formatAccountBalance = (
  balance: number,
  equity: number,
  currency: string = 'USD'
): { balance: string; equity: string; difference: string; isPositive: boolean } => {
  const balanceFormatted = formatCurrency(balance, currency)
  const equityFormatted = formatCurrency(equity, currency)
  const difference = equity - balance
  const differenceFormatted = formatCurrency(Math.abs(difference), currency)
  
  return {
    balance: balanceFormatted,
    equity: equityFormatted,
    difference: `${difference >= 0 ? '+' : '-'}${differenceFormatted}`,
    isPositive: difference >= 0
  }
}
