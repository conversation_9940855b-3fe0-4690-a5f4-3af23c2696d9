// Date and Time Formatting Utilities
// Comprehensive date/time formatting for trading applications

/**
 * Format date with various presets
 */
export const formatDate = (
  date: Date | string | number,
  format: 'short' | 'medium' | 'long' | 'full' | 'iso' | 'custom' = 'medium',
  locale: string = 'en-US',
  customFormat?: Intl.DateTimeFormatOptions
): string => {
  const dateObj = new Date(date)
  
  if (isNaN(dateObj.getTime())) {
    return 'Invalid Date'
  }
  
  if (format === 'iso') {
    return dateObj.toISOString()
  }
  
  if (format === 'custom' && customFormat) {
    try {
      return new Intl.DateTimeFormat(locale, customFormat).format(dateObj)
    } catch (error) {
      console.warn('Date formatting error:', error)
      return dateObj.toLocaleDateString(locale)
    }
  }
  
  const formatOptions: Record<string, Intl.DateTimeFormatOptions> = {
    short: { month: 'numeric', day: 'numeric', year: '2-digit' },
    medium: { month: 'short', day: 'numeric', year: 'numeric' },
    long: { month: 'long', day: 'numeric', year: 'numeric' },
    full: { weekday: 'long', month: 'long', day: 'numeric', year: 'numeric' }
  }
  
  try {
    return new Intl.DateTimeFormat(locale, formatOptions[format]).format(dateObj)
  } catch (error) {
    return dateObj.toLocaleDateString(locale)
  }
}

/**
 * Format time with various presets
 */
export const formatTime = (
  date: Date | string | number,
  format: 'short' | 'medium' | 'long' | 'seconds' | '24h' = 'medium',
  locale: string = 'en-US',
  timezone?: string
): string => {
  const dateObj = new Date(date)
  
  if (isNaN(dateObj.getTime())) {
    return 'Invalid Time'
  }
  
  const formatOptions: Record<string, Intl.DateTimeFormatOptions> = {
    short: { hour: 'numeric', minute: '2-digit' },
    medium: { hour: 'numeric', minute: '2-digit', second: '2-digit' },
    long: { hour: 'numeric', minute: '2-digit', second: '2-digit', timeZoneName: 'short' },
    seconds: { hour: '2-digit', minute: '2-digit', second: '2-digit', hour12: false },
    '24h': { hour: '2-digit', minute: '2-digit', hour12: false }
  }
  
  const options = { ...formatOptions[format] }
  if (timezone) {
    options.timeZone = timezone
  }
  
  try {
    return new Intl.DateTimeFormat(locale, options).format(dateObj)
  } catch (error) {
    return dateObj.toLocaleTimeString(locale)
  }
}

/**
 * Format datetime with various presets
 */
export const formatDateTime = (
  date: Date | string | number,
  format: 'short' | 'medium' | 'long' | 'full' = 'medium',
  locale: string = 'en-US',
  timezone?: string
): string => {
  const dateObj = new Date(date)
  
  if (isNaN(dateObj.getTime())) {
    return 'Invalid DateTime'
  }
  
  const formatOptions: Record<string, Intl.DateTimeFormatOptions> = {
    short: { 
      month: 'numeric', 
      day: 'numeric', 
      year: '2-digit',
      hour: 'numeric',
      minute: '2-digit'
    },
    medium: { 
      month: 'short', 
      day: 'numeric', 
      year: 'numeric',
      hour: 'numeric',
      minute: '2-digit'
    },
    long: { 
      month: 'long', 
      day: 'numeric', 
      year: 'numeric',
      hour: 'numeric',
      minute: '2-digit',
      second: '2-digit'
    },
    full: { 
      weekday: 'long',
      month: 'long', 
      day: 'numeric', 
      year: 'numeric',
      hour: 'numeric',
      minute: '2-digit',
      second: '2-digit',
      timeZoneName: 'short'
    }
  }
  
  const options = { ...formatOptions[format] }
  if (timezone) {
    options.timeZone = timezone
  }
  
  try {
    return new Intl.DateTimeFormat(locale, options).format(dateObj)
  } catch (error) {
    return dateObj.toLocaleString(locale)
  }
}

/**
 * Format relative time (e.g., "2 hours ago", "in 3 days")
 */
export const formatRelativeTime = (
  date: Date | string | number,
  locale: string = 'en-US'
): string => {
  const dateObj = new Date(date)
  const now = new Date()
  
  if (isNaN(dateObj.getTime())) {
    return 'Invalid Date'
  }
  
  const diffMs = dateObj.getTime() - now.getTime()
  const diffSeconds = Math.floor(diffMs / 1000)
  const diffMinutes = Math.floor(diffSeconds / 60)
  const diffHours = Math.floor(diffMinutes / 60)
  const diffDays = Math.floor(diffHours / 24)
  
  try {
    const rtf = new Intl.RelativeTimeFormat(locale, { numeric: 'auto' })
    
    if (Math.abs(diffDays) >= 1) {
      return rtf.format(diffDays, 'day')
    } else if (Math.abs(diffHours) >= 1) {
      return rtf.format(diffHours, 'hour')
    } else if (Math.abs(diffMinutes) >= 1) {
      return rtf.format(diffMinutes, 'minute')
    } else {
      return rtf.format(diffSeconds, 'second')
    }
  } catch (error) {
    // Fallback for older browsers
    const absSeconds = Math.abs(diffSeconds)
    const absMinutes = Math.abs(diffMinutes)
    const absHours = Math.abs(diffHours)
    const absDays = Math.abs(diffDays)
    
    const suffix = diffMs > 0 ? 'from now' : 'ago'
    
    if (absDays >= 1) {
      return `${absDays} day${absDays !== 1 ? 's' : ''} ${suffix}`
    } else if (absHours >= 1) {
      return `${absHours} hour${absHours !== 1 ? 's' : ''} ${suffix}`
    } else if (absMinutes >= 1) {
      return `${absMinutes} minute${absMinutes !== 1 ? 's' : ''} ${suffix}`
    } else {
      return `${absSeconds} second${absSeconds !== 1 ? 's' : ''} ${suffix}`
    }
  }
}

/**
 * Format trading session time
 */
export const formatTradingSession = (
  openTime: string,
  closeTime: string,
  timezone: string = 'UTC'
): string => {
  try {
    const today = new Date()
    const open = new Date(`${today.toDateString()} ${openTime}`)
    const close = new Date(`${today.toDateString()} ${closeTime}`)
    
    const openFormatted = formatTime(open, 'short', 'en-US', timezone)
    const closeFormatted = formatTime(close, 'short', 'en-US', timezone)
    
    return `${openFormatted} - ${closeFormatted} ${timezone}`
  } catch (error) {
    return `${openTime} - ${closeTime} ${timezone}`
  }
}

/**
 * Format expiry time for binary options
 */
export const formatExpiryTime = (
  expiryDate: Date | string | number,
  showCountdown: boolean = true
): { formatted: string; countdown?: string; isExpired: boolean } => {
  const expiry = new Date(expiryDate)
  const now = new Date()
  
  if (isNaN(expiry.getTime())) {
    return { formatted: 'Invalid Date', isExpired: true }
  }
  
  const isExpired = expiry.getTime() <= now.getTime()
  const formatted = formatDateTime(expiry, 'medium')
  
  let countdown: string | undefined
  if (showCountdown && !isExpired) {
    const diffMs = expiry.getTime() - now.getTime()
    countdown = formatCountdown(diffMs)
  }
  
  return { formatted, countdown, isExpired }
}

/**
 * Format countdown timer
 */
export const formatCountdown = (milliseconds: number): string => {
  if (milliseconds <= 0) return '00:00:00'
  
  const totalSeconds = Math.floor(milliseconds / 1000)
  const hours = Math.floor(totalSeconds / 3600)
  const minutes = Math.floor((totalSeconds % 3600) / 60)
  const seconds = totalSeconds % 60
  
  if (hours > 0) {
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
  } else {
    return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
  }
}

/**
 * Format market hours status
 */
export const formatMarketStatus = (
  openTime: string,
  closeTime: string,
  timezone: string = 'UTC',
  currentTime?: Date
): { status: 'open' | 'closed' | 'pre-market' | 'after-hours'; nextChange: string } => {
  const now = currentTime || new Date()
  const today = now.toDateString()
  
  try {
    const open = new Date(`${today} ${openTime}`)
    const close = new Date(`${today} ${closeTime}`)
    
    // Convert to specified timezone (simplified)
    const currentHour = now.getHours()
    const openHour = open.getHours()
    const closeHour = close.getHours()
    
    let status: 'open' | 'closed' | 'pre-market' | 'after-hours'
    let nextChange: string
    
    if (currentHour >= openHour && currentHour < closeHour) {
      status = 'open'
      nextChange = `Closes at ${formatTime(close, 'short')}`
    } else if (currentHour < openHour) {
      status = 'pre-market'
      nextChange = `Opens at ${formatTime(open, 'short')}`
    } else {
      status = 'after-hours'
      // Next day opening
      const nextOpen = new Date(open)
      nextOpen.setDate(nextOpen.getDate() + 1)
      nextChange = `Opens ${formatRelativeTime(nextOpen)}`
    }
    
    return { status, nextChange }
  } catch (error) {
    return { status: 'closed', nextChange: 'Unknown' }
  }
}

/**
 * Format time until market event
 */
export const formatTimeUntilEvent = (
  eventTime: Date | string | number,
  eventName: string = 'Event'
): string => {
  const event = new Date(eventTime)
  const now = new Date()
  
  if (isNaN(event.getTime())) {
    return 'Invalid event time'
  }
  
  const diffMs = event.getTime() - now.getTime()
  
  if (diffMs <= 0) {
    return `${eventName} has passed`
  }
  
  const days = Math.floor(diffMs / (1000 * 60 * 60 * 24))
  const hours = Math.floor((diffMs % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
  const minutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60))
  
  if (days > 0) {
    return `${eventName} in ${days}d ${hours}h`
  } else if (hours > 0) {
    return `${eventName} in ${hours}h ${minutes}m`
  } else {
    return `${eventName} in ${minutes}m`
  }
}

/**
 * Get trading day boundaries
 */
export const getTradingDay = (
  date: Date = new Date(),
  timezone: string = 'UTC'
): { start: Date; end: Date } => {
  const tradingDate = new Date(date)
  
  // Set to start of trading day (typically 00:00 UTC for forex)
  const start = new Date(tradingDate)
  start.setUTCHours(0, 0, 0, 0)
  
  // Set to end of trading day
  const end = new Date(start)
  end.setUTCHours(23, 59, 59, 999)
  
  return { start, end }
}

/**
 * Check if date is weekend
 */
export const isWeekend = (date: Date = new Date()): boolean => {
  const day = date.getDay()
  return day === 0 || day === 6 // Sunday or Saturday
}

/**
 * Check if date is market holiday
 */
export const isMarketHoliday = (
  date: Date,
  holidays: Date[] = []
): boolean => {
  const dateString = date.toDateString()
  return holidays.some(holiday => holiday.toDateString() === dateString)
}

/**
 * Get next trading day
 */
export const getNextTradingDay = (
  date: Date = new Date(),
  holidays: Date[] = []
): Date => {
  let nextDay = new Date(date)
  nextDay.setDate(nextDay.getDate() + 1)
  
  while (isWeekend(nextDay) || isMarketHoliday(nextDay, holidays)) {
    nextDay.setDate(nextDay.getDate() + 1)
  }
  
  return nextDay
}

/**
 * Format timezone offset
 */
export const formatTimezoneOffset = (date: Date = new Date()): string => {
  const offset = date.getTimezoneOffset()
  const hours = Math.floor(Math.abs(offset) / 60)
  const minutes = Math.abs(offset) % 60
  const sign = offset <= 0 ? '+' : '-'
  
  return `UTC${sign}${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`
}
