// API Response and Request Types
// Comprehensive type definitions for all API interactions

import type {
  BinaryOption,
  TradingAccount,
  TradingStrategy,
  TechnicalIndicatorConfig,
  TradingRule,
  RiskManagementConfig,
  BacktestResult,
  TradeHistory,
  PortfolioMetrics,
  EconomicEvent,
  TradingAlert,
  SignalProvider,
  CopyTradingConfig,
  AutoTradingConfig
} from './trading'

export interface AuthRequest {
  email: string
  password: string
  rememberMe?: boolean
}

export interface AuthResponse {
  token: string
  refreshToken: string
  user: UserProfile
  expiresIn: number
}

export interface UserProfile {
  id: string
  email: string
  firstName: string
  lastName: string
  avatar?: string
  country: string
  timezone: string
  language: string
  isVerified: boolean
  accountType: 'DEMO' | 'LIVE'
  registrationDate: Date
  lastLoginDate: Date
  preferences: UserPreferences
}

export interface CreatePositionRequest {
  symbol: string
  direction: TradingDirection
  amount: number
  expiryTime: Date
  strategyId?: string
}

export interface CreatePositionResponse {
  position: BinaryOption
  account: TradingAccount
}

export interface ClosePositionRequest {
  positionId: string
  reason?: string
}

export interface ClosePositionResponse {
  position: BinaryOption
  account: TradingAccount
  profit: number
}

export interface GetPositionsRequest {
  status?: OrderStatus
  symbol?: string
  startDate?: Date
  endDate?: Date
  page?: number
  limit?: number
}

export interface GetPositionsResponse extends PaginatedResponse<BinaryOption> {
  summary: {
    totalProfit: number
    totalLoss: number
    winRate: number
    totalTrades: number
  }
}

export interface GetMarketDataRequest {
  symbols: string[]
  timeFrame?: TimeFrame
  startDate?: Date
  endDate?: Date
  includeIndicators?: boolean
}

export interface GetMarketDataResponse {
  data: Record<string, CandlestickData[]>
  indicators?: Record<string, TechnicalIndicator[]>
  timestamp: Date
}

export interface GetSignalsRequest {
  symbols?: string[]
  timeFrames?: TimeFrame[]
  minConfidence?: number
  page?: number
  limit?: number
}

export interface GetSignalsResponse extends PaginatedResponse<TradingSignal> {
  activeSignals: number
  averageConfidence: number
}

export interface CreateStrategyRequest {
  name: string
  description: string
  timeFrames: TimeFrame[]
  indicators: TechnicalIndicatorConfig[]
  entryRules: TradingRule[]
  exitRules: TradingRule[]
  riskManagement: RiskManagementConfig
}

export interface CreateStrategyResponse {
  strategy: TradingStrategy
  backtestResult?: BacktestResult
}

export interface BacktestRequest {
  strategyId: string
  symbol: string
  startDate: Date
  endDate: Date
  initialBalance: number
  timeFrame: TimeFrame
}

export interface BacktestResponse {
  result: BacktestResult
  trades: TradeHistory[]
  equityCurve: { date: Date; equity: number }[]
  drawdownCurve: { date: Date; drawdown: number }[]
}

export interface GetAccountRequest {
  includeMetrics?: boolean
  includePositions?: boolean
}

export interface GetAccountResponse {
  account: TradingAccount
  metrics?: PortfolioMetrics
  positions?: BinaryOption[]
}

export interface UpdateAccountRequest {
  leverage?: number
  currency?: string
  riskSettings?: RiskManagementConfig
}

export interface GetEconomicEventsRequest {
  startDate?: Date
  endDate?: Date
  currencies?: string[]
  impact?: ('LOW' | 'MEDIUM' | 'HIGH')[]
}

export interface GetEconomicEventsResponse {
  events: EconomicEvent[]
  upcomingEvents: EconomicEvent[]
  highImpactCount: number
}

export interface GetAlertsRequest {
  type?: TradingAlert['type']
  isRead?: boolean
  page?: number
  limit?: number
}

export interface GetAlertsResponse extends PaginatedResponse<TradingAlert> {
  unreadCount: number
}

export interface CreateAlertRequest {
  type: TradingAlert['type']
  title: string
  message: string
  severity: TradingAlert['severity']
  symbol?: string
}

export interface UpdateAlertRequest {
  alertId: string
  isRead?: boolean
}

export interface GetSignalProvidersRequest {
  minWinRate?: number
  maxRiskScore?: number
  verified?: boolean
  page?: number
  limit?: number
}

export interface GetSignalProvidersResponse extends PaginatedResponse<SignalProvider> {
  topProviders: SignalProvider[]
  averageWinRate: number
}

export interface SubscribeToProviderRequest {
  providerId: string
  config: CopyTradingConfig
}

export interface SubscribeToProviderResponse {
  subscription: {
    id: string
    providerId: string
    config: CopyTradingConfig
    startDate: Date
    isActive: boolean
  }
}

export interface GetCopyTradingRequest {
  includePerformance?: boolean
}

export interface GetCopyTradingResponse {
  subscriptions: Array<{
    id: string
    provider: SignalProvider
    config: CopyTradingConfig
    performance: {
      totalProfit: number
      winRate: number
      totalTrades: number
      monthlyReturn: number
    }
    isActive: boolean
  }>
}

export interface UpdateAutoTradingRequest {
  config: AutoTradingConfig
}

export interface GetAutoTradingResponse {
  config: AutoTradingConfig
  status: {
    isRunning: boolean
    activeTrades: number
    todaysTrades: number
    todaysProfit: number
    lastTradeTime?: Date
  }
}

export interface GetChartDataRequest {
  symbol: string
  timeFrame: TimeFrame
  startDate?: Date
  endDate?: Date
  candleCount?: number
  includeIndicators?: boolean
}

export interface GetChartDataResponse {
  candles: CandlestickData[]
  indicators?: Record<string, TechnicalIndicator[]>
  volume: number[]
  symbol: string
  timeFrame: TimeFrame
}

export interface WebSocketSubscribeRequest {
  channels: string[]
  symbols?: string[]
}

export interface WebSocketUnsubscribeRequest {
  channels: string[]
  symbols?: string[]
}

export interface WebSocketMessage<T = unknown> {
  channel: string
  event: string
  data: T
  timestamp: Date
}

export interface RealTimePriceUpdate {
  symbol: string
  bid: number
  ask: number
  price: number
  change: number
  changePercent: number
  volume: number
  timestamp: Date
}

export interface RealTimePositionUpdate {
  positionId: string
  currentPrice: number
  unrealizedPnL: number
  timestamp: Date
}

export interface RealTimeAccountUpdate {
  balance: number
  equity: number
  margin: number
  freeMargin: number
  marginLevel: number
  timestamp: Date
}
