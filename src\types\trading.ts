// Trading-Specific Interfaces and Types
// These extend the global types with more detailed trading functionality

export interface BinaryOption {
  id: string
  symbol: string
  direction: TradingDirection
  amount: number
  strikePrice: number
  expiryTime: Date
  openTime: Date
  status: OrderStatus
  payout: number
  payoutPercentage: number
  isWin?: boolean
  actualClosePrice?: number
}

export interface TradingAccount {
  id: string
  balance: number
  equity: number
  margin: number
  freeMargin: number
  marginLevel: number
  currency: string
  leverage: number
  totalProfit: number
  totalLoss: number
  winRate: number
  totalTrades: number
}

export interface TradingStrategy {
  id: string
  name: string
  description: string
  timeFrames: TimeFrame[]
  indicators: TechnicalIndicatorConfig[]
  entryRules: TradingRule[]
  exitRules: TradingRule[]
  riskManagement: RiskManagementConfig
  backtestResults?: BacktestResult
  isActive: boolean
}

export interface TechnicalIndicatorConfig {
  type: 'SMA' | 'EMA' | 'RSI' | 'MACD' | 'BB' | 'STOCH' | 'ADX'
  period: number
  parameters: Record<string, number>
  signal: 'BUY' | 'SELL' | 'NEUTRAL'
}

export interface TradingRule {
  id: string
  condition: string // e.g., "RSI < 30 AND SMA_20 > SMA_50"
  action: 'ENTER_LONG' | 'ENTER_SHORT' | 'EXIT' | 'HOLD'
  weight: number // 0-1, importance of this rule
}

export interface RiskManagementConfig {
  maxRiskPerTrade: number // percentage of account
  maxDailyLoss: number // percentage of account
  maxOpenPositions: number
  stopLossPercentage?: number
  takeProfitPercentage?: number
  trailingStop?: boolean
  martingaleEnabled?: boolean
  martingaleMultiplier?: number
  maxMartingaleSteps?: number
}

export interface BacktestResult {
  startDate: Date
  endDate: Date
  totalTrades: number
  winningTrades: number
  losingTrades: number
  winRate: number
  totalProfit: number
  totalLoss: number
  netProfit: number
  maxDrawdown: number
  sharpeRatio: number
  profitFactor: number
  averageWin: number
  averageLoss: number
  largestWin: number
  largestLoss: number
}

export interface MarketSession {
  name: string
  timezone: string
  openTime: string // HH:MM format
  closeTime: string // HH:MM format
  isActive: boolean
  volume: number
  volatility: number
}

export interface EconomicEvent {
  id: string
  title: string
  country: string
  currency: string
  impact: 'LOW' | 'MEDIUM' | 'HIGH'
  actual?: number
  forecast?: number
  previous?: number
  timestamp: Date
  description: string
}

export interface TradingAlert {
  id: string
  type: 'SIGNAL' | 'NEWS' | 'RISK' | 'SYSTEM'
  title: string
  message: string
  severity: 'INFO' | 'WARNING' | 'ERROR'
  symbol?: string
  timestamp: Date
  isRead: boolean
  actionRequired?: boolean
}

export interface PortfolioMetrics {
  totalValue: number
  dailyPnL: number
  dailyPnLPercentage: number
  weeklyPnL: number
  monthlyPnL: number
  yearlyPnL: number
  maxDrawdown: number
  sharpeRatio: number
  winRate: number
  averageWin: number
  averageLoss: number
  profitFactor: number
  totalTrades: number
  activeTrades: number
}

export interface ChartConfiguration {
  symbol: string
  timeFrame: TimeFrame
  candleCount: number
  indicators: TechnicalIndicatorConfig[]
  overlays: string[]
  theme: 'light' | 'dark'
  showVolume: boolean
  showGrid: boolean
}

export interface OrderBook {
  symbol: string
  bids: OrderBookEntry[]
  asks: OrderBookEntry[]
  timestamp: Date
}

export interface OrderBookEntry {
  price: number
  volume: number
  count: number
}

export interface TradeHistory {
  id: string
  symbol: string
  direction: TradingDirection
  amount: number
  entryPrice: number
  exitPrice: number
  openTime: Date
  closeTime: Date
  profit: number
  profitPercentage: number
  commission: number
  swap: number
  strategy?: string
  notes?: string
}

export interface SignalProvider {
  id: string
  name: string
  description: string
  winRate: number
  totalSignals: number
  followers: number
  monthlyReturn: number
  maxDrawdown: number
  riskScore: number
  isVerified: boolean
  subscriptionPrice: number
  rating: number
}

export interface CopyTradingConfig {
  providerId: string
  isActive: boolean
  copyRatio: number // 0-1, percentage of provider's trade size
  maxRiskPerTrade: number
  stopCopyOnDrawdown: number
  allowedSymbols: string[]
  excludedSymbols: string[]
  maxDailyTrades: number
}

export interface AutoTradingConfig {
  isEnabled: boolean
  strategies: string[] // strategy IDs
  maxConcurrentTrades: number
  tradingHours: {
    start: string
    end: string
    timezone: string
  }
  allowedSymbols: string[]
  riskLimits: RiskManagementConfig
  emergencyStop: {
    maxDailyLoss: number
    maxDrawdown: number
    enabled: boolean
  }
}
