// API Client Utilities
// Centralized HTTP client with authentication, error handling, and request/response interceptors

interface RequestConfig {
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH'
  headers?: Record<string, string>
  body?: unknown
  timeout?: number
  retries?: number
  retryDelay?: number
}

interface RequestInterceptor {
  onRequest?: (config: RequestConfig) => RequestConfig | Promise<RequestConfig>
  onError?: (error: Error) => Error | Promise<Error>
}

interface ResponseInterceptor {
  onResponse?: <T>(response: ApiResponse<T>) => ApiResponse<T> | Promise<ApiResponse<T>>
  onError?: (error: ApiError) => ApiError | Promise<ApiError>
}

class ApiClient {
  private baseURL: string
  private defaultHeaders: Record<string, string>
  private requestInterceptors: RequestInterceptor[] = []
  private responseInterceptors: ResponseInterceptor[] = []
  private authToken: string | null = null

  constructor(baseURL: string, defaultHeaders: Record<string, string> = {}) {
    this.baseURL = baseURL.replace(/\/$/, '') // Remove trailing slash
    this.defaultHeaders = {
      'Content-Type': 'application/json',
      ...defaultHeaders
    }
  }

  /**
   * Set authentication token
   */
  setAuthToken(token: string | null): void {
    this.authToken = token
  }

  /**
   * Add request interceptor
   */
  addRequestInterceptor(interceptor: RequestInterceptor): void {
    this.requestInterceptors.push(interceptor)
  }

  /**
   * Add response interceptor
   */
  addResponseInterceptor(interceptor: ResponseInterceptor): void {
    this.responseInterceptors.push(interceptor)
  }

  /**
   * Make HTTP request
   */
  async request<T = unknown>(
    endpoint: string,
    config: RequestConfig = {}
  ): Promise<ApiResponse<T>> {
    const url = `${this.baseURL}${endpoint.startsWith('/') ? endpoint : `/${endpoint}`}`
    
    let requestConfig: RequestConfig = {
      method: 'GET',
      timeout: 30000,
      retries: 3,
      retryDelay: 1000,
      ...config
    }

    // Apply request interceptors
    for (const interceptor of this.requestInterceptors) {
      if (interceptor.onRequest) {
        try {
          requestConfig = await interceptor.onRequest(requestConfig)
        } catch (error) {
          if (interceptor.onError) {
            throw await interceptor.onError(error as Error)
          }
          throw error
        }
      }
    }

    // Prepare headers
    const headers: Record<string, string> = {
      ...this.defaultHeaders,
      ...requestConfig.headers
    }

    if (this.authToken) {
      headers.Authorization = `Bearer ${this.authToken}`
    }

    // Prepare fetch options
    const fetchOptions: RequestInit = {
      method: requestConfig.method,
      headers,
      signal: AbortSignal.timeout(requestConfig.timeout!)
    }

    if (requestConfig.body && requestConfig.method !== 'GET') {
      fetchOptions.body = JSON.stringify(requestConfig.body)
    }

    // Execute request with retries
    let lastError: Error | null = null
    
    for (let attempt = 0; attempt <= requestConfig.retries!; attempt++) {
      try {
        const response = await fetch(url, fetchOptions)
        const responseData = await this.parseResponse<T>(response)
        
        // Apply response interceptors
        let finalResponse = responseData
        for (const interceptor of this.responseInterceptors) {
          if (interceptor.onResponse) {
            finalResponse = await interceptor.onResponse(finalResponse)
          }
        }
        
        return finalResponse
      } catch (error) {
        lastError = error as Error
        
        // Apply error interceptors
        for (const interceptor of this.responseInterceptors) {
          if (interceptor.onError && this.isApiError(error)) {
            lastError = await interceptor.onError(error as ApiError)
          }
        }
        
        // Retry logic
        if (attempt < requestConfig.retries! && this.shouldRetry(error as Error)) {
          await this.delay(requestConfig.retryDelay! * Math.pow(2, attempt))
          continue
        }
        
        break
      }
    }

    throw lastError
  }

  /**
   * GET request
   */
  async get<T = unknown>(endpoint: string, config?: Omit<RequestConfig, 'method' | 'body'>): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { ...config, method: 'GET' })
  }

  /**
   * POST request
   */
  async post<T = unknown>(endpoint: string, body?: unknown, config?: Omit<RequestConfig, 'method'>): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { ...config, method: 'POST', body })
  }

  /**
   * PUT request
   */
  async put<T = unknown>(endpoint: string, body?: unknown, config?: Omit<RequestConfig, 'method'>): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { ...config, method: 'PUT', body })
  }

  /**
   * DELETE request
   */
  async delete<T = unknown>(endpoint: string, config?: Omit<RequestConfig, 'method' | 'body'>): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { ...config, method: 'DELETE' })
  }

  /**
   * PATCH request
   */
  async patch<T = unknown>(endpoint: string, body?: unknown, config?: Omit<RequestConfig, 'method'>): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { ...config, method: 'PATCH', body })
  }

  /**
   * Parse response
   */
  private async parseResponse<T>(response: Response): Promise<ApiResponse<T>> {
    const timestamp = new Date()
    
    try {
      const data = await response.json()
      
      if (!response.ok) {
        throw {
          code: `HTTP_${response.status}`,
          message: data.message || response.statusText,
          details: data,
          timestamp
        } as ApiError
      }
      
      return {
        success: true,
        data,
        timestamp
      }
    } catch (error) {
      if (this.isApiError(error)) {
        throw error
      }
      
      throw {
        code: 'PARSE_ERROR',
        message: 'Failed to parse response',
        details: error,
        timestamp
      } as ApiError
    }
  }

  /**
   * Check if error should trigger retry
   */
  private shouldRetry(error: Error): boolean {
    if (this.isApiError(error)) {
      const apiError = error as ApiError
      // Retry on server errors (5xx) and network errors
      return apiError.code.startsWith('HTTP_5') || 
             apiError.code === 'NETWORK_ERROR' ||
             apiError.code === 'TIMEOUT_ERROR'
    }
    
    return error.name === 'AbortError' || 
           error.name === 'TypeError' // Network errors
  }

  /**
   * Check if error is ApiError
   */
  private isApiError(error: unknown): boolean {
    return typeof error === 'object' && 
           error !== null && 
           'code' in error && 
           'message' in error
  }

  /**
   * Delay utility for retries
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }
}

/**
 * Create default API client instance
 */
export const createApiClient = (baseURL: string, headers?: Record<string, string>): ApiClient => {
  const client = new ApiClient(baseURL, headers)
  
  // Add default request interceptor for logging
  client.addRequestInterceptor({
    onRequest: (config) => {
      console.debug(`API Request: ${config.method} ${baseURL}`, config)
      return config
    },
    onError: (error) => {
      console.error('API Request Error:', error)
      return error
    }
  })
  
  // Add default response interceptor for logging
  client.addResponseInterceptor({
    onResponse: (response) => {
      console.debug('API Response:', response)
      return response
    },
    onError: (error) => {
      console.error('API Response Error:', error)
      return error
    }
  })
  
  return client
}

/**
 * Default API client instance
 */
export const apiClient = createApiClient(
  process.env.VITE_API_URL || 'http://localhost:3000/api'
)

/**
 * Authentication interceptor
 */
export const addAuthInterceptor = (client: ApiClient): void => {
  client.addRequestInterceptor({
    onRequest: (config) => {
      const token = localStorage.getItem('authToken')
      if (token) {
        client.setAuthToken(token)
      }
      return config
    }
  })
  
  client.addResponseInterceptor({
    onError: async (error) => {
      if (error.code === 'HTTP_401') {
        // Token expired, try to refresh
        try {
          const refreshToken = localStorage.getItem('refreshToken')
          if (refreshToken) {
            const response = await client.post<AuthResponse>('/auth/refresh', { refreshToken })
            if (response.success && response.data) {
              localStorage.setItem('authToken', response.data.token)
              localStorage.setItem('refreshToken', response.data.refreshToken)
              client.setAuthToken(response.data.token)
            }
          }
        } catch (refreshError) {
          // Refresh failed, redirect to login
          localStorage.removeItem('authToken')
          localStorage.removeItem('refreshToken')
          window.location.href = '/login'
        }
      }
      return error
    }
  })
}

/**
 * Rate limiting interceptor
 */
export const addRateLimitInterceptor = (client: ApiClient, requestsPerSecond: number = 10): void => {
  const requestQueue: Array<() => void> = []
  let isProcessing = false
  
  const processQueue = async (): Promise<void> => {
    if (isProcessing || requestQueue.length === 0) return
    
    isProcessing = true
    const request = requestQueue.shift()
    
    if (request) {
      request()
      await new Promise(resolve => setTimeout(resolve, 1000 / requestsPerSecond))
    }
    
    isProcessing = false
    
    if (requestQueue.length > 0) {
      processQueue()
    }
  }
  
  client.addRequestInterceptor({
    onRequest: (config) => {
      return new Promise((resolve) => {
        requestQueue.push(() => resolve(config))
        processQueue()
      })
    }
  })
}

export default ApiClient
