import chalk from 'chalk'

type LogLevel = 'debug' | 'info' | 'success' | 'warn' | 'error'

interface LoggerOptions {
  showTimestamp?: boolean
  showLevel?: boolean
  prefix?: string
}

class Logger {
  private showTimestamp: boolean = true
  private showLevel: boolean = true
  private prefix: string = ''

  constructor(options: LoggerOptions = {}) {
    this.showTimestamp = options.showTimestamp ?? true
    this.showLevel = options.showLevel ?? true
    this.prefix = options.prefix ?? ''
  }

  log(level: LogLevel, ...args: unknown[]): void {
    const parts: string[] = []

    const timestamp = new Date().toISOString()

    if (this.showTimestamp) {
      parts.push(chalk.gray(`[${timestamp}]`))
    }

    if (this.showLevel) {
      const levelText = `[${level.toUpperCase()}]`
      parts.push(this.getColoredLevel(level, levelText))
    }

    if (this.prefix !== '') {
      parts.push(chalk.cyan(`[${this.prefix}]`))
    }

    const logMessage = parts.join(' ')

    switch (level) {
      case 'debug':
        console.debug(logMessage, ...args)
        break
      case 'info':
        console.log(logMessage, ...args)
        break
      case 'success':
        console.log(logMessage, ...args)
        break
      case 'warn':
        console.warn(logMessage, ...args)
        break
      case 'error':
        console.error(logMessage, ...args)
        break
    }
  }

  debug(...args: unknown[]): void {
    this.log('debug', ...args)
  }

  info(...args: unknown[]): void {
    this.log('info', ...args)
  }

  success(...args: unknown[]): void {
    this.log('success', ...args)
  }

  warn(...args: unknown[]): void {
    this.log('warn', ...args)
  }

  error(...args: unknown[]): void {
    this.log('error', ...args)
  }

  private getColoredLevel(level: LogLevel, text: string): string {
    switch (level) {
      case 'debug':
        return chalk.gray(text)
      case 'info':
        return chalk.cyan(text)
      case 'success':
        return chalk.green(text)
      case 'warn':
        return chalk.yellow(text)
      case 'error':
        return chalk.red(text)
    }
  }
}

export default Logger
