import { darkGray, cyan, green, yellow, red } from './../../../node_modules/ansicolor/ansicolor.d'
type LogLevel = 'debug' | 'info' | 'success' | 'warn' | 'error'

interface LoggerOptions {
  showTimestamp?: boolean
  showLevel?: boolean
  prefix?: string
}

class Logger {
  private showTimestamp: boolean = true
  private showLevel: boolean = true
  private prefix: string = ''

  constructor(options: LoggerOptions) {
    this.showTimestamp = options.showTimestamp ?? true
    this.showLevel = options.showLevel ?? true
    this.prefix = options.prefix ?? ''
  }

  log(level: LogLevel, ...args: unknown[]): void {
    let message = ''

    const timestamp = new Date().toISOString()

    if (this.showTimestamp) {
      message += darkGray(`[${timestamp}] `)
    }

    if (this.showLevel) {
      message += `[${level.toUpperCase()}] `
    }

    if (this.prefix !== '') {
      message += cyan(`[${this.prefix}] `)
    }

    const colorFn = this.getColor(level)
    const logMessage = colorFn(message)

    switch (level) {
      case 'debug':
        console.debug(logMessage, ...args)
        break
      case 'info':
        console.log(logMessage, ...args)
        break
      case 'success':
        console.log(logMessage, ...args)
        break
      case 'warn':
        console.warn(logMessage, ...args)
        break
      case 'error':
        console.error(logMessage, ...args)
        break
    }
  }

  debug(...args: unknown[]): void {
    this.log('debug', ...args)
  }

  info(...args: unknown[]): void {
    this.log('info', ...args)
  }

  success(...args: unknown[]): void {
    this.log('success', ...args)
  }

  warn(...args: unknown[]): void {
    this.log('warn', ...args)
  }

  error(...args: unknown[]): void {
    this.log('error', ...args)
  }

  private getColor(level: LogLevel): (text: string) => string {
    switch (level) {
      case 'debug':
        return darkGray
      case 'info':
        return cyan
      case 'success':
        return green
      case 'warn':
        return yellow
      case 'error':
        return red
    }
  }
}

export default Logger
