# WebSocket Client Documentation

A comprehensive, production-ready WebSocket client implementation using socket.io-client with advanced features for robust real-time communication.

## Enhanced Features

### Core Features

- ✅ **Socket.io-client integration** - Built on the reliable socket.io-client library
- ✅ **TypeScript support** - Full type safety and IntelliSense with comprehensive type definitions
- ✅ **Singleton pattern** - Single instance management for application-wide use
- ✅ **Event-driven architecture** - Clean event-based communication pattern

### Advanced Connection Management

- ✅ **Intelligent connection state management** - Proper state synchronization with socket.io
- ✅ **Event listener queue system** - Automatic queuing when disconnected, processing on reconnection
- ✅ **Event buffering** - Buffer outgoing events during disconnection with replay on reconnection
- ✅ **Exponential backoff reconnection** - Smart reconnection with configurable limits and delays
- ✅ **Connection health monitoring** - Real-time health checks with latency tracking and timeout detection

### Error Handling & Monitoring

- ✅ **Comprehensive error tracking** - Detailed error history with context and timestamps
- ✅ **Multi-level logging** - Debug, info, warn, error levels with context broadcasting
- ✅ **Performance metrics** - Connection uptime, latency, reconnection statistics
- ✅ **Automatic error recovery** - Self-healing mechanisms for common connection issues

## Installation

The required dependencies are already installed:

- `socket.io-client` - WebSocket client library
- `@types/socket.io-client` - TypeScript definitions

## Quick Start

### Basic Usage

```typescript
import WebSocketClient from './services/websocket/WebSocketClient'

// Get singleton instance
const client = WebSocketClient.getInstance()

// Set up event listeners (automatically queued if not connected)
client.on('successupdateBalance', (data) => {
  console.log('Balance updated:', data)
})

client.on('updateAssets', (data) => {
  console.log('Assets updated:', data)
})

// Connect (authentication handled automatically)
client.connect()

// Send messages (automatically buffered if disconnected)
client.send('placeOrder', {
  amount: 100,
  asset: 'EURUSD',
  direction: 'up'
})

// Check connection status
if (client.isConnected()) {
  console.log('Connected and ready')
}

// Get health status
const health = client.getHealthStatus()
console.log('Connection health:', health)

// Get connection metrics
const metrics = client.getConnectionMetrics()
console.log('Connection metrics:', metrics)
```

### Environment Configuration

Create a `.env` file:

```env
WEBSOCKET_SERVER_URL=ws://localhost:3000
WEBSOCKET_SESSION=your_session_id
WEBSOCKET_IS_DEMO=1
WEBSOCKET_UID=12345
WEBSOCKET_PLATFORM=2
WEBSOCKET_IS_FAST_HISTORY=true
WEBSOCKET_TIMEOUT=5000
WEBSOCKET_AUTO_RECONNECT=true
WEBSOCKET_MAX_RECONNECT_ATTEMPTS=5
WEBSOCKET_RECONNECT_DELAY=1000
```

Then use environment-based configuration:

```typescript
const config = WebSocketConfigManager.createConfig()
const client = new WebSocketClient(config)
```

## Configuration

### Authentication Payload

The authentication payload follows this structure:

```typescript
interface AuthPayload {
  session: string // Session identifier
  isDemo: number // Demo mode (0 = false, 1 = true)
  uid: string // User identifier
  platform: number // Platform identifier
  isFastHistory: boolean // Fast history flag
}
```

### WebSocket Configuration

```typescript
interface WebSocketConfig {
  serverUrl: string // Server URL to connect to
  auth: AuthPayload // Authentication payload
  timeout?: number // Connection timeout (default: 5000ms)
  autoReconnect?: boolean // Auto-reconnect (default: true)
  maxReconnectAttempts?: number // Max reconnect attempts (default: 5)
  reconnectDelay?: number // Reconnect delay (default: 1000ms)
}
```

## API Reference

### WebSocketClient

#### Constructor

```typescript
new WebSocketClient(config: WebSocketConfig)
```

#### Properties

- `state: ConnectionState` - Current connection state (readonly)

#### Methods

##### Core Connection Methods

- `connect(): void` - Connect to the WebSocket server
- `disconnect(): void` - Disconnect from the WebSocket server
- `isConnected(): boolean` - Check if currently connected
- `getConnectionStatus(): WSConnectionState` - Get current connection state

##### Event Management

- `on<K extends keyof WebSocketEvents>(event: K, listener: WebSocketEvents[K]): void` - Listen for events (queued if disconnected)
- `once(event: string, listener: WSCallback): void` - Listen for event once (queued if disconnected)
- `send(event: string, data?: unknown): void` - Send message (buffered if disconnected)

##### Monitoring & Diagnostics

- `getHealthStatus(): WSHealthStatus` - Get current connection health status
- `getConnectionMetrics(): WSConnectionMetrics` - Get connection performance metrics
- `getLastError(): WSError | null` - Get last error information
- `getErrorHistory(): WSError[]` - Get error history
- `getAccountBalance(): WSAccountBalance | null` - Get current account balance
- `getChartSettings(): WSChartSettings | null` - Get current chart settings

#### Events

##### Connection Events

- `connect` - Connection established
- `disconnect` - Connection lost
- `error` - Connection error
- `reconnect` - Reconnection successful
- `reconnect_failed` - Reconnection failed

##### Trading Events

- `successupdateBalance` - Account balance updated
- `updateAssets` - Trading assets updated
- `message` - Custom message received

##### System Events

- `ws:health` - Connection health status update
- `ws:error` - Error notification with details
- `ws:log` - Log message from WebSocket client

### WebSocketConfigManager

#### Static Methods

##### `createConfig(overrides?: Partial<WebSocketConfig>): WebSocketConfig`

Create a complete configuration with defaults and environment variables.

##### `loadFromEnvironment(): Partial<WebSocketConfig>`

Load configuration from environment variables.

##### `validateConfig(config: WebSocketConfig): { isValid: boolean; errors: string[] }`

Validate configuration and return validation results.

##### `generateEnvExample(): string`

Generate example environment file content.

## Advanced Features

### Event Listener Queue System

The client automatically queues event listeners when the socket is not connected:

```typescript
// These listeners will be queued and attached when connection is established
client.on('successupdateBalance', (data) => {
  console.log('Balance:', data)
})

client.on('updateAssets', (data) => {
  console.log('Assets:', data)
})

// Connect later - queued listeners will be processed
client.connect()
```

### Event Buffering

Outgoing events are automatically buffered when disconnected and replayed on reconnection:

```typescript
// These events will be buffered if disconnected
client.send('placeOrder', { amount: 100 })
client.send('updateSettings', { theme: 'dark' })

// Events are automatically sent when connection is restored
```

### Connection Health Monitoring

Monitor connection health in real-time:

```typescript
const health = client.getHealthStatus()
console.log('Health:', {
  isHealthy: health.isHealthy,
  latency: health.latency,
  uptime: health.uptime,
  issues: health.issues
})

// Listen for health updates
client.on('ws:health', (health) => {
  if (!health.isHealthy) {
    console.warn('Connection issues:', health.issues)
  }
})
```

### Performance Metrics

Track connection performance:

```typescript
const metrics = client.getConnectionMetrics()
console.log('Metrics:', {
  connectAttempts: metrics.connectAttempts,
  totalReconnects: metrics.totalReconnects,
  averageLatency: metrics.averageLatency,
  connectionUptime: metrics.connectionUptime
})
```

## Connection States

```typescript
enum WSConnectionState {
  DISCONNECTED = 'disconnected',
  CONNECTING = 'connecting',
  CONNECTED = 'connected',
  RECONNECTING = 'reconnecting',
  ERROR = 'error'
}
```

## Examples

### React Component Example

See `src/renderer/src/examples/WebSocketExample.tsx` for a complete React component that demonstrates:

- Configuration management
- Connection controls
- Message sending/receiving
- Real-time status updates

### Simple Usage Examples

See `src/renderer/src/examples/simple-usage.ts` for various usage patterns:

- Basic usage
- Configuration manager usage
- Environment-based configuration
- Custom authentication scenarios
- Error handling

## Error Handling

The client provides comprehensive error handling:

```typescript
client.on('error', (error) => {
  console.error('WebSocket error:', error.message)
  // Handle error (show notification, retry, etc.)
})

client.on('reconnect_failed', () => {
  console.error('Failed to reconnect after maximum attempts')
  // Handle permanent connection failure
})

try {
  await client.connect()
} catch (error) {
  console.error('Initial connection failed:', error)
  // Handle initial connection failure
}
```

## Best Practices

1. **Always validate configuration** before creating the client
2. **Set up error handlers** before connecting
3. **Use environment variables** for sensitive configuration
4. **Handle reconnection scenarios** gracefully
5. **Clean up connections** when components unmount
6. **Log connection state changes** for debugging

## Troubleshooting

### Common Issues

1. **Connection timeout**: Increase the `timeout` value in configuration
2. **Authentication failed**: Verify the auth payload structure and values
3. **Reconnection issues**: Check `maxReconnectAttempts` and `reconnectDelay` settings
4. **Environment variables not loaded**: Ensure proper environment setup

### Debug Logging

The client provides detailed console logging for:

- Connection state changes
- Message sending/receiving
- Error conditions
- Reconnection attempts

Enable debug mode by checking browser console for WebSocket-related logs.
