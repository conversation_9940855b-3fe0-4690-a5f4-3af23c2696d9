import { describe, it, expect, beforeEach, afterEach, vi, Mock } from 'vitest'
import { WSConnectionState } from '../../../types/websocket/enums'
import WebSocketClient from './WebSocketClient'
import { io } from 'socket.io-client'
import { BrowserWindow } from 'electron'

// Mock dependencies
vi.mock('socket.io-client')
vi.mock('electron', () => {
  const mockBrowserWindow = {
    getAllWindows: vi.fn(() => [])
  }
  return {
    BrowserWindow: mockBrowserWindow
  }
})
vi.mock('./Auth', () => ({
  default: {
    getInstance: vi.fn(() => ({
      getAuthPayload: vi.fn(() => ({
        session: 'test-session',
        isDemo: 1,
        uid: 12345,
        platform: 0,
        isFastHistory: false
      }))
    }))
  }
}))
vi.mock('../../../utils/payload', () => ({
  formatPayload: vi.fn((data) => data)
}))

describe('WebSocketClient', () => {
  let client: WebSocketClient
  let mockSocket: any
  let mockBrowserWindow: any

  beforeEach(() => {
    // Reset all mocks
    vi.clearAllMocks()

    // Mock socket.io client
    mockSocket = {
      on: vi.fn(),
      once: vi.fn(),
      emit: vi.fn(),
      disconnect: vi.fn(),
      removeAllListeners: vi.fn(),
      onAny: vi.fn(),
      connected: true
    }
    ;(io as Mock).mockReturnValue(mockSocket)

    // Mock BrowserWindow
    mockBrowserWindow = {
      webContents: {
        send: vi.fn()
      }
    }
    ;(BrowserWindow.getAllWindows as Mock).mockReturnValue([mockBrowserWindow])

    // Get fresh instance
    client = WebSocketClient.getInstance()
  })

  afterEach(() => {
    if (client) {
      client.disconnect()
    }
  })

  describe('Singleton Pattern', () => {
    it('should return the same instance', () => {
      const instance1 = WebSocketClient.getInstance()
      const instance2 = WebSocketClient.getInstance()
      expect(instance1).toBe(instance2)
    })
  })

  describe('Connection Management', () => {
    it('should initialize with DISCONNECTED state', () => {
      expect(client.getConnectionStatus()).toBe(WSConnectionState.DISCONNECTED)
      expect(client.isConnected()).toBe(false)
    })

    it('should set CONNECTING state when connecting', () => {
      client.connect()
      expect(client.getConnectionStatus()).toBe(WSConnectionState.CONNECTING)
    })

    it('should create socket with correct configuration', () => {
      client.connect()

      expect(io).toHaveBeenCalledWith('wss://demo-api-eu.po.market', {
        transports: ['websocket'],
        query: {
          EIO: '4',
          transport: ['websocket']
        },
        extraHeaders: {
          Origin: 'https://pocketoption.com'
        },
        path: '/socket.io/'
      })
    })

    it('should not connect if already connected', () => {
      // Simulate connected state
      client.connect()
      const firstCallCount = (io as Mock).mock.calls.length

      // Try to connect again
      client.connect()
      expect((io as Mock).mock.calls.length).toBe(firstCallCount)
    })

    it('should setup event handlers on connect', () => {
      client.connect()

      expect(mockSocket.on).toHaveBeenCalledWith('connect', expect.any(Function))
      expect(mockSocket.on).toHaveBeenCalledWith('successauth', expect.any(Function))
      expect(mockSocket.on).toHaveBeenCalledWith('disconnect', expect.any(Function))
      expect(mockSocket.on).toHaveBeenCalledWith('connect_error', expect.any(Function))
    })
  })

  describe('Event Listener Queue System', () => {
    it('should queue event listeners when not connected', () => {
      const listener = vi.fn()

      // Try to attach listener when disconnected
      client.on('successupdateBalance', listener)

      // Should not attach to socket immediately
      expect(mockSocket.on).not.toHaveBeenCalledWith('successupdateBalance', expect.any(Function))
    })

    it('should attach listeners immediately when connected', () => {
      // Simulate connection
      client.connect()

      // Simulate successful auth
      const authHandler = mockSocket.on.mock.calls.find((call) => call[0] === 'successauth')[1]
      authHandler()

      const listener = vi.fn()
      client.on('successupdateBalance', listener)

      // Should attach to socket immediately
      expect(mockSocket.on).toHaveBeenCalledWith('successupdateBalance', expect.any(Function))
    })

    it('should process pending listeners after connection', () => {
      const listener = vi.fn()

      // Queue listener while disconnected
      client.on('successupdateBalance', listener)

      // Connect and authenticate
      client.connect()
      const authHandler = mockSocket.on.mock.calls.find((call) => call[0] === 'successauth')[1]
      authHandler()

      // Should process pending listeners
      expect(mockSocket.on).toHaveBeenCalledWith('successupdateBalance', expect.any(Function))
    })
  })

  describe('Event Buffering', () => {
    it('should buffer events when disconnected', () => {
      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {})

      client.send('testEvent', { data: 'test' })

      // Should not emit to socket when disconnected
      expect(mockSocket.emit).not.toHaveBeenCalled()

      consoleSpy.mockRestore()
    })

    it('should send events immediately when connected', () => {
      // Simulate connection
      client.connect()
      const authHandler = mockSocket.on.mock.calls.find((call) => call[0] === 'successauth')[1]
      authHandler()

      client.send('testEvent', { data: 'test' })

      expect(mockSocket.emit).toHaveBeenCalledWith('testEvent', { data: 'test' })
    })

    it('should not buffer system events', () => {
      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {})

      client.send('auth', { session: 'test' })
      client.send('ps')

      // System events should not be buffered
      expect(mockSocket.emit).not.toHaveBeenCalled()

      consoleSpy.mockRestore()
    })
  })

  describe('Connection Health Monitoring', () => {
    it('should provide health status', () => {
      const health = client.getHealthStatus()

      expect(health).toHaveProperty('isHealthy')
      expect(health).toHaveProperty('latency')
      expect(health).toHaveProperty('uptime')
      expect(health).toHaveProperty('lastHeartbeat')
      expect(health).toHaveProperty('issues')
    })

    it('should report unhealthy when disconnected', () => {
      const health = client.getHealthStatus()

      expect(health.isHealthy).toBe(false)
      expect(health.issues).toContain('Connection not established: disconnected')
    })

    it('should track connection metrics', () => {
      const metrics = client.getConnectionMetrics()

      expect(metrics).toHaveProperty('connectAttempts')
      expect(metrics).toHaveProperty('lastConnectTime')
      expect(metrics).toHaveProperty('lastDisconnectTime')
      expect(metrics).toHaveProperty('totalReconnects')
      expect(metrics).toHaveProperty('averageLatency')
    })
  })

  describe('Error Handling', () => {
    it('should track errors', () => {
      // Simulate connection error
      client.connect()
      const errorHandler = mockSocket.on.mock.calls.find((call) => call[0] === 'connect_error')[1]
      errorHandler(new Error('Connection failed'))

      const lastError = client.getLastError()
      expect(lastError).toBeTruthy()

      const errorHistory = client.getErrorHistory()
      expect(errorHistory.length).toBeGreaterThan(0)
    })

    it('should handle send errors gracefully', () => {
      // Simulate connected state but socket error
      client.connect()
      const authHandler = mockSocket.on.mock.calls.find((call) => call[0] === 'successauth')[1]
      authHandler()

      mockSocket.emit.mockImplementation(() => {
        throw new Error('Socket error')
      })

      expect(() => client.send('testEvent', {})).not.toThrow()
    })
  })

  describe('Reconnection Logic', () => {
    it('should attempt reconnection on disconnect', () => {
      vi.useFakeTimers()

      client.connect()

      // Simulate disconnect
      const disconnectHandler = mockSocket.on.mock.calls.find((call) => call[0] === 'disconnect')[1]
      disconnectHandler('transport close')

      expect(client.getConnectionStatus()).toBe(WSConnectionState.RECONNECTING)

      vi.useRealTimers()
    })

    it('should use exponential backoff for reconnection', () => {
      vi.useFakeTimers()

      const metrics = client.getConnectionMetrics()
      const initialAttempts = metrics.connectAttempts

      client.connect()

      // Simulate multiple connection failures
      const errorHandler = mockSocket.on.mock.calls.find((call) => call[0] === 'connect_error')[1]

      errorHandler(new Error('Connection failed'))
      vi.advanceTimersByTime(1000) // First retry delay

      errorHandler(new Error('Connection failed'))
      vi.advanceTimersByTime(2000) // Second retry delay (exponential backoff)

      const updatedMetrics = client.getConnectionMetrics()
      expect(updatedMetrics.connectAttempts).toBeGreaterThan(initialAttempts)

      vi.useRealTimers()
    })
  })

  describe('Edge Cases and Error Scenarios', () => {
    it('should handle socket creation failure', () => {
      ;(io as Mock).mockImplementation(() => {
        throw new Error('Socket creation failed')
      })

      expect(() => client.connect()).not.toThrow()
      expect(client.getConnectionStatus()).toBe(WSConnectionState.ERROR)
    })

    it('should handle malformed event data', () => {
      client.connect()
      const authHandler = mockSocket.on.mock.calls.find((call) => call[0] === 'successauth')[1]
      authHandler()

      const listener = vi.fn()
      client.on('successupdateBalance', listener)

      // Simulate malformed data
      const balanceHandler = mockSocket.on.mock.calls.find(
        (call) => call[0] === 'successupdateBalance'
      )[1]

      expect(() => balanceHandler(null)).not.toThrow()
      expect(() => balanceHandler(undefined)).not.toThrow()
      expect(() => balanceHandler({ invalid: 'data' })).not.toThrow()
    })

    it('should handle event buffer overflow', () => {
      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {})

      // Fill buffer beyond limit
      for (let i = 0; i < 150; i++) {
        client.send(`event${i}`, { data: i })
      }

      // Should not crash and should maintain buffer size limit
      expect(() => client.send('finalEvent', {})).not.toThrow()

      consoleSpy.mockRestore()
    })

    it('should handle rapid connect/disconnect cycles', () => {
      for (let i = 0; i < 10; i++) {
        client.connect()
        client.disconnect()
      }

      expect(client.getConnectionStatus()).toBe(WSConnectionState.DISCONNECTED)
    })

    it('should handle listener attachment with invalid event names', () => {
      client.connect()
      const authHandler = mockSocket.on.mock.calls.find((call) => call[0] === 'successauth')[1]
      authHandler()

      expect(() => client.on('' as any, vi.fn())).not.toThrow()
      expect(() => client.on(null as any, vi.fn())).not.toThrow()
      expect(() => client.on(undefined as any, vi.fn())).not.toThrow()
    })

    it('should handle heartbeat when socket is null', () => {
      vi.useFakeTimers()

      client.connect()
      client.disconnect() // This sets socket to null

      // Fast forward to trigger heartbeat
      expect(() => vi.advanceTimersByTime(20000)).not.toThrow()

      vi.useRealTimers()
    })

    it('should handle event processing errors gracefully', () => {
      client.connect()
      const authHandler = mockSocket.on.mock.calls.find((call) => call[0] === 'successauth')[1]
      authHandler()

      const faultyListener = vi.fn(() => {
        throw new Error('Listener error')
      })

      client.on('successupdateBalance', faultyListener)

      const balanceHandler = mockSocket.on.mock.calls.find(
        (call) => call[0] === 'successupdateBalance'
      )[1]

      expect(() => balanceHandler({ balance: 100 })).not.toThrow()
    })
  })

  describe('Cleanup', () => {
    it('should cleanup resources on disconnect', () => {
      client.connect()
      client.disconnect()

      expect(mockSocket.disconnect).toHaveBeenCalled()
      expect(client.getConnectionStatus()).toBe(WSConnectionState.DISCONNECTED)
    })

    it('should clear event listeners on disconnect', () => {
      client.connect()

      // Add some listeners
      client.on('successupdateBalance', vi.fn())

      client.disconnect()

      expect(mockSocket.removeAllListeners).toHaveBeenCalled()
    })

    it('should clear all timers on disconnect', () => {
      vi.useFakeTimers()

      client.connect()
      const authHandler = mockSocket.on.mock.calls.find((call) => call[0] === 'successauth')[1]
      authHandler()

      client.disconnect()

      // Should not have any active timers
      expect(() => vi.advanceTimersByTime(100000)).not.toThrow()

      vi.useRealTimers()
    })
  })
})
