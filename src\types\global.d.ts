// Global Type Definitions for Kwartani Trading Platform
// All types/interfaces are globally accessible without imports

declare global {
  // ============================================================================
  // UTILITY TYPES
  // ============================================================================

  type LogLevel = 'debug' | 'info' | 'success' | 'warn' | 'error'

  interface LoggerOptions {
    showTimestamp?: boolean
    showLevel?: boolean
    prefix?: string
  }

  // ============================================================================
  // TRADING TYPES
  // ============================================================================

  type TradingDirection = 'CALL' | 'PUT'
  type OrderStatus = 'PENDING' | 'ACTIVE' | 'CLOSED' | 'CANCELLED'
  type MarketState = 'OPEN' | 'CLOSED' | 'PRE_MARKET' | 'AFTER_HOURS'
  type TimeFrame = '1m' | '5m' | '15m' | '30m' | '1h' | '4h' | '1d'

  interface TradingPosition {
    id: string
    symbol: string
    direction: TradingDirection
    amount: number
    entryPrice: number
    exitPrice?: number
    openTime: Date
    closeTime?: Date
    status: OrderStatus
    profit?: number
    payout?: number
  }

  interface MarketData {
    symbol: string
    price: number
    timestamp: Date
    bid: number
    ask: number
    volume: number
    change: number
    changePercent: number
  }

  interface TradingSignal {
    id: string
    symbol: string
    direction: TradingDirection
    confidence: number
    timeFrame: TimeFrame
    entryPrice: number
    expiryTime: Date
    indicators: string[]
    timestamp: Date
  }

  // ============================================================================
  // API TYPES
  // ============================================================================

  interface ApiResponse<T = unknown> {
    success: boolean
    data?: T
    error?: string
    message?: string
    timestamp: Date
  }

  interface ApiError extends Error {
    code: string
    message: string
    details?: unknown
    timestamp: Date
  }

  interface PaginatedResponse<T> {
    data: T[]
    total: number
    page: number
    limit: number
    hasNext: boolean
    hasPrev: boolean
  }

  // ============================================================================
  // COMPONENT TYPES
  // ============================================================================

  interface BaseComponentProps {
    className?: string
    children?: React.ReactNode
  }

  interface ButtonProps extends BaseComponentProps {
    variant?: 'primary' | 'secondary' | 'success' | 'warning' | 'danger'
    size?: 'sm' | 'md' | 'lg'
    disabled?: boolean
    loading?: boolean
    onClick?: () => void
  }

  interface ModalProps extends BaseComponentProps {
    isOpen: boolean
    onClose: () => void
    title?: string
    size?: 'sm' | 'md' | 'lg' | 'xl'
  }

  // ============================================================================
  // CONFIGURATION TYPES
  // ============================================================================

  interface AppConfig {
    apiUrl: string
    wsUrl: string
    environment: 'development' | 'staging' | 'production'
    features: {
      autoTrading: boolean
      socialTrading: boolean
      advancedCharts: boolean
    }
    trading: {
      maxPositions: number
      maxRiskPerTrade: number
      defaultTimeFrame: TimeFrame
    }
  }

  interface UserPreferences {
    theme: 'light' | 'dark'
    language: string
    notifications: {
      trades: boolean
      signals: boolean
      news: boolean
    }
    trading: {
      confirmOrders: boolean
      autoClose: boolean
      riskWarnings: boolean
    }
  }

  // ============================================================================
  // MATHEMATICAL TYPES
  // ============================================================================

  interface TechnicalIndicator {
    name: string
    value: number
    signal: 'BUY' | 'SELL' | 'NEUTRAL'
    timestamp: Date
  }

  interface CandlestickData {
    timestamp: Date
    open: number
    high: number
    low: number
    close: number
    volume: number
  }

  interface MovingAverage {
    period: number
    value: number
    type: 'SMA' | 'EMA' | 'WMA'
  }

  // ============================================================================
  // VALIDATION TYPES
  // ============================================================================

  interface ValidationRule {
    required?: boolean
    min?: number
    max?: number
    pattern?: RegExp
    custom?: (value: unknown) => boolean | string
  }

  interface ValidationResult {
    isValid: boolean
    errors: string[]
  }

  interface FormField {
    name: string
    value: unknown
    rules: ValidationRule[]
    error?: string
  }

  // ============================================================================
  // EVENT TYPES
  // ============================================================================

  interface CustomEvent<T = unknown> {
    type: string
    data: T
    timestamp: Date
  }

  interface TradingEvent extends CustomEvent {
    type: 'POSITION_OPENED' | 'POSITION_CLOSED' | 'SIGNAL_GENERATED' | 'MARKET_UPDATE'
    data: TradingPosition | TradingSignal | MarketData
  }

  // ============================================================================
  // UTILITY FUNCTION TYPES
  // ============================================================================

  type FormatFunction = (value: unknown) => string
  type ValidationFunction = (value: unknown) => ValidationResult
  type CalculationFunction = (...args: number[]) => number
  type EventHandler<T = unknown> = (event: CustomEvent<T>) => void
}

export {}
