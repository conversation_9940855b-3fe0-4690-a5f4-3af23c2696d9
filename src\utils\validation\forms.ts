// Form Validation Utilities
// Comprehensive validation functions for trading forms and user inputs

/**
 * Validate email address
 */
export const validateEmail = (email: string): ValidationResult => {
  const errors: string[] = []
  
  if (!email) {
    errors.push('Email is required')
  } else {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(email)) {
      errors.push('Please enter a valid email address')
    }
  }
  
  return { isValid: errors.length === 0, errors }
}

/**
 * Validate password strength
 */
export const validatePassword = (password: string): ValidationResult => {
  const errors: string[] = []
  
  if (!password) {
    errors.push('Password is required')
  } else {
    if (password.length < 8) {
      errors.push('Password must be at least 8 characters long')
    }
    if (!/[A-Z]/.test(password)) {
      errors.push('Password must contain at least one uppercase letter')
    }
    if (!/[a-z]/.test(password)) {
      errors.push('Password must contain at least one lowercase letter')
    }
    if (!/\d/.test(password)) {
      errors.push('Password must contain at least one number')
    }
    if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
      errors.push('Password must contain at least one special character')
    }
  }
  
  return { isValid: errors.length === 0, errors }
}

/**
 * Validate trading amount
 */
export const validateTradingAmount = (
  amount: number,
  minAmount: number = 1,
  maxAmount: number = 10000,
  accountBalance: number = Infinity
): ValidationResult => {
  const errors: string[] = []
  
  if (isNaN(amount) || amount <= 0) {
    errors.push('Amount must be a positive number')
  } else {
    if (amount < minAmount) {
      errors.push(`Minimum amount is ${minAmount}`)
    }
    if (amount > maxAmount) {
      errors.push(`Maximum amount is ${maxAmount}`)
    }
    if (amount > accountBalance) {
      errors.push('Insufficient account balance')
    }
  }
  
  return { isValid: errors.length === 0, errors }
}

/**
 * Validate expiry time for binary options
 */
export const validateExpiryTime = (
  expiryTime: Date,
  minExpiryMinutes: number = 1,
  maxExpiryHours: number = 24
): ValidationResult => {
  const errors: string[] = []
  const now = new Date()
  const diffMs = expiryTime.getTime() - now.getTime()
  const diffMinutes = diffMs / (1000 * 60)
  const diffHours = diffMinutes / 60
  
  if (isNaN(expiryTime.getTime())) {
    errors.push('Invalid expiry time')
  } else {
    if (diffMinutes < minExpiryMinutes) {
      errors.push(`Expiry time must be at least ${minExpiryMinutes} minute(s) from now`)
    }
    if (diffHours > maxExpiryHours) {
      errors.push(`Expiry time cannot be more than ${maxExpiryHours} hours from now`)
    }
  }
  
  return { isValid: errors.length === 0, errors }
}

/**
 * Validate leverage ratio
 */
export const validateLeverage = (
  leverage: number,
  minLeverage: number = 1,
  maxLeverage: number = 500
): ValidationResult => {
  const errors: string[] = []
  
  if (isNaN(leverage) || leverage <= 0) {
    errors.push('Leverage must be a positive number')
  } else {
    if (leverage < minLeverage) {
      errors.push(`Minimum leverage is ${minLeverage}:1`)
    }
    if (leverage > maxLeverage) {
      errors.push(`Maximum leverage is ${maxLeverage}:1`)
    }
  }
  
  return { isValid: errors.length === 0, errors }
}

/**
 * Validate stop loss percentage
 */
export const validateStopLoss = (
  stopLossPercentage: number,
  minStopLoss: number = 0.1,
  maxStopLoss: number = 50
): ValidationResult => {
  const errors: string[] = []
  
  if (isNaN(stopLossPercentage) || stopLossPercentage <= 0) {
    errors.push('Stop loss must be a positive number')
  } else {
    if (stopLossPercentage < minStopLoss) {
      errors.push(`Minimum stop loss is ${minStopLoss}%`)
    }
    if (stopLossPercentage > maxStopLoss) {
      errors.push(`Maximum stop loss is ${maxStopLoss}%`)
    }
  }
  
  return { isValid: errors.length === 0, errors }
}

/**
 * Validate take profit percentage
 */
export const validateTakeProfit = (
  takeProfitPercentage: number,
  minTakeProfit: number = 0.1,
  maxTakeProfit: number = 1000
): ValidationResult => {
  const errors: string[] = []
  
  if (isNaN(takeProfitPercentage) || takeProfitPercentage <= 0) {
    errors.push('Take profit must be a positive number')
  } else {
    if (takeProfitPercentage < minTakeProfit) {
      errors.push(`Minimum take profit is ${minTakeProfit}%`)
    }
    if (takeProfitPercentage > maxTakeProfit) {
      errors.push(`Maximum take profit is ${maxTakeProfit}%`)
    }
  }
  
  return { isValid: errors.length === 0, errors }
}

/**
 * Validate risk percentage per trade
 */
export const validateRiskPercentage = (
  riskPercentage: number,
  maxRisk: number = 10
): ValidationResult => {
  const errors: string[] = []
  
  if (isNaN(riskPercentage) || riskPercentage <= 0) {
    errors.push('Risk percentage must be a positive number')
  } else {
    if (riskPercentage > maxRisk) {
      errors.push(`Risk per trade cannot exceed ${maxRisk}% of account balance`)
    }
  }
  
  return { isValid: errors.length === 0, errors }
}

/**
 * Validate phone number
 */
export const validatePhoneNumber = (phoneNumber: string): ValidationResult => {
  const errors: string[] = []
  
  if (!phoneNumber) {
    errors.push('Phone number is required')
  } else {
    // Remove all non-digit characters for validation
    const digitsOnly = phoneNumber.replace(/\D/g, '')
    
    if (digitsOnly.length < 10) {
      errors.push('Phone number must be at least 10 digits')
    }
    if (digitsOnly.length > 15) {
      errors.push('Phone number cannot exceed 15 digits')
    }
  }
  
  return { isValid: errors.length === 0, errors }
}

/**
 * Validate date of birth (must be 18+ years old)
 */
export const validateDateOfBirth = (dateOfBirth: Date): ValidationResult => {
  const errors: string[] = []
  const today = new Date()
  const age = today.getFullYear() - dateOfBirth.getFullYear()
  const monthDiff = today.getMonth() - dateOfBirth.getMonth()
  
  if (isNaN(dateOfBirth.getTime())) {
    errors.push('Invalid date of birth')
  } else {
    const actualAge = monthDiff < 0 || (monthDiff === 0 && today.getDate() < dateOfBirth.getDate()) 
      ? age - 1 
      : age
    
    if (actualAge < 18) {
      errors.push('You must be at least 18 years old')
    }
    if (actualAge > 120) {
      errors.push('Please enter a valid date of birth')
    }
  }
  
  return { isValid: errors.length === 0, errors }
}

/**
 * Validate required field
 */
export const validateRequired = (value: unknown, fieldName: string = 'Field'): ValidationResult => {
  const errors: string[] = []
  
  if (value === null || value === undefined || value === '') {
    errors.push(`${fieldName} is required`)
  }
  
  return { isValid: errors.length === 0, errors }
}

/**
 * Validate numeric range
 */
export const validateNumericRange = (
  value: number,
  min: number,
  max: number,
  fieldName: string = 'Value'
): ValidationResult => {
  const errors: string[] = []
  
  if (isNaN(value)) {
    errors.push(`${fieldName} must be a number`)
  } else {
    if (value < min) {
      errors.push(`${fieldName} must be at least ${min}`)
    }
    if (value > max) {
      errors.push(`${fieldName} cannot exceed ${max}`)
    }
  }
  
  return { isValid: errors.length === 0, errors }
}

/**
 * Validate string length
 */
export const validateStringLength = (
  value: string,
  minLength: number = 0,
  maxLength: number = Infinity,
  fieldName: string = 'Field'
): ValidationResult => {
  const errors: string[] = []
  
  if (typeof value !== 'string') {
    errors.push(`${fieldName} must be a string`)
  } else {
    if (value.length < minLength) {
      errors.push(`${fieldName} must be at least ${minLength} characters`)
    }
    if (value.length > maxLength) {
      errors.push(`${fieldName} cannot exceed ${maxLength} characters`)
    }
  }
  
  return { isValid: errors.length === 0, errors }
}

/**
 * Validate URL format
 */
export const validateURL = (url: string): ValidationResult => {
  const errors: string[] = []
  
  if (!url) {
    errors.push('URL is required')
  } else {
    try {
      new URL(url)
    } catch {
      errors.push('Please enter a valid URL')
    }
  }
  
  return { isValid: errors.length === 0, errors }
}

/**
 * Validate file upload
 */
export const validateFileUpload = (
  file: File,
  allowedTypes: string[] = [],
  maxSizeMB: number = 10
): ValidationResult => {
  const errors: string[] = []
  
  if (!file) {
    errors.push('File is required')
  } else {
    // Check file type
    if (allowedTypes.length > 0 && !allowedTypes.includes(file.type)) {
      errors.push(`File type must be one of: ${allowedTypes.join(', ')}`)
    }
    
    // Check file size
    const maxSizeBytes = maxSizeMB * 1024 * 1024
    if (file.size > maxSizeBytes) {
      errors.push(`File size cannot exceed ${maxSizeMB}MB`)
    }
  }
  
  return { isValid: errors.length === 0, errors }
}

/**
 * Validate form with multiple fields
 */
export const validateForm = (fields: FormField[]): ValidationResult => {
  const allErrors: string[] = []
  
  for (const field of fields) {
    for (const rule of field.rules) {
      if (rule.required && (field.value === null || field.value === undefined || field.value === '')) {
        allErrors.push(`${field.name} is required`)
        continue
      }
      
      if (field.value !== null && field.value !== undefined && field.value !== '') {
        if (rule.min !== undefined && typeof field.value === 'number' && field.value < rule.min) {
          allErrors.push(`${field.name} must be at least ${rule.min}`)
        }
        
        if (rule.max !== undefined && typeof field.value === 'number' && field.value > rule.max) {
          allErrors.push(`${field.name} cannot exceed ${rule.max}`)
        }
        
        if (rule.pattern && typeof field.value === 'string' && !rule.pattern.test(field.value)) {
          allErrors.push(`${field.name} format is invalid`)
        }
        
        if (rule.custom) {
          const customResult = rule.custom(field.value)
          if (typeof customResult === 'string') {
            allErrors.push(customResult)
          } else if (!customResult) {
            allErrors.push(`${field.name} is invalid`)
          }
        }
      }
    }
  }
  
  return { isValid: allErrors.length === 0, errors: allErrors }
}

/**
 * Validate trading symbol format
 */
export const validateTradingSymbol = (symbol: string): ValidationResult => {
  const errors: string[] = []
  
  if (!symbol) {
    errors.push('Trading symbol is required')
  } else {
    // Basic symbol format validation (e.g., EURUSD, AAPL, BTCUSD)
    const symbolRegex = /^[A-Z]{3,10}$/
    if (!symbolRegex.test(symbol)) {
      errors.push('Trading symbol must be 3-10 uppercase letters')
    }
  }
  
  return { isValid: errors.length === 0, errors }
}

/**
 * Validate API key format
 */
export const validateApiKey = (apiKey: string): ValidationResult => {
  const errors: string[] = []
  
  if (!apiKey) {
    errors.push('API key is required')
  } else {
    if (apiKey.length < 16) {
      errors.push('API key must be at least 16 characters')
    }
    if (apiKey.length > 128) {
      errors.push('API key cannot exceed 128 characters')
    }
    // Check for valid characters (alphanumeric and common special chars)
    const apiKeyRegex = /^[A-Za-z0-9\-_=+/]+$/
    if (!apiKeyRegex.test(apiKey)) {
      errors.push('API key contains invalid characters')
    }
  }
  
  return { isValid: errors.length === 0, errors }
}
