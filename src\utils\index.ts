// Global Utility Functions Export
// All utilities are globally available via single import

// Trading Utilities
export * from './trading/calculations'
export * from './trading/indicators'

// API Utilities
export * from './api/client'

// Format Utilities
export * from './format/currency'
export * from './format/date'

// Validation Utilities
export * from './validation/forms'

// Mathematical Utilities
export * from './math/statistics'

// Payload Utilities
export * from './payload'

// Logger Utility
export { default as Logger } from '../shared/utility/Logger'

// Re-export commonly used functions with shorter names for convenience
import {
  calculatePositionSize,
  calculateBinaryOptionPnL,
  calculateWinRate,
  calculateProfitFactor,
  calculateMaxDrawdown,
  calculateSharpeRatio,
  calculateKellyCriterion,
  calculateCAGR,
  calculateVolatility,
  calculateCorrelation,
  calculateBeta,
  calculateVaR,
  calculateExpectedShortfall,
  calculateInformationRatio,
  calculateSortinoRatio
} from './trading/calculations'

import {
  calculateSMA,
  calculateEMA,
  calculateRSI,
  calculateMACD,
  calculateBollingerBands,
  calculateStochastic,
  calculateADX,
  calculateWilliamsR,
  calculateCCI,
  calculateMFI,
  calculateParabolicSAR
} from './trading/indicators'

import {
  formatCurrency,
  formatPercentage,
  formatCompactNumber,
  formatNumber,
  formatPnL,
  formatPrice,
  formatVolume,
  formatMarketCap,
  formatSpread,
  formatDuration,
  formatRisk,
  formatWinRate,
  formatLeverage,
  formatMargin,
  formatPipValue,
  formatOrderSize,
  formatAccountBalance
} from './format/currency'

import {
  formatDate,
  formatTime,
  formatDateTime,
  formatRelativeTime,
  formatCountdown,
  formatTradingSession,
  formatExpiryTime,
  formatMarketStatus,
  formatTimeUntilEvent,
  getTradingDay,
  isWeekend,
  isMarketHoliday,
  getNextTradingDay,
  formatTimezoneOffset
} from './format/date'

import {
  validateEmail,
  validatePassword,
  validateTradingAmount,
  validateExpiryTime,
  validateLeverage,
  validateStopLoss,
  validateTakeProfit,
  validateRiskPercentage,
  validatePhoneNumber,
  validateDateOfBirth,
  validateRequired,
  validateNumericRange,
  validateStringLength,
  validateURL,
  validateFileUpload,
  validateForm,
  validateTradingSymbol,
  validateApiKey
} from './validation/forms'

import {
  calculateMean,
  calculateMedian,
  calculateMode,
  calculateStandardDeviation,
  calculateVariance,
  calculateSkewness,
  calculateKurtosis,
  calculatePercentile,
  calculateQuartiles,
  calculateIQR,
  detectOutliers,
  calculateZScores,
  normalizeValues,
  calculateMovingAverage,
  calculateExponentialMovingAverage,
  calculateLinearRegression,
  calculateCorrelationCoefficient,
  calculateCovariance,
  calculateConfidenceInterval,
  performTTest,
  calculateRollingStatistics
} from './math/statistics'

import { createApiClient, apiClient } from './api/client'
import { formatPayload, validatePayload, sanitizePayload } from './payload'

// Short aliases
export const positionSize = calculatePositionSize
export const binaryPnL = calculateBinaryOptionPnL
export const winRate = calculateWinRate
export const profitFactor = calculateProfitFactor
export const maxDrawdown = calculateMaxDrawdown
export const sharpeRatio = calculateSharpeRatio
export const kellyCriterion = calculateKellyCriterion

export const sma = calculateSMA
export const ema = calculateEMA
export const rsi = calculateRSI
export const macd = calculateMACD
export const bollingerBands = calculateBollingerBands

export const currency = formatCurrency
export const percentage = formatPercentage
export const compact = formatCompactNumber
export const pnl = formatPnL
export const price = formatPrice

export const date = formatDate
export const time = formatTime
export const dateTime = formatDateTime
export const relativeTime = formatRelativeTime
export const countdown = formatCountdown

export const isValidEmail = validateEmail
export const isValidPassword = validatePassword
export const isValidAmount = validateTradingAmount
export const isRequired = validateRequired

export const mean = calculateMean
export const median = calculateMedian
export const stdDev = calculateStandardDeviation
export const correlation = calculateCorrelationCoefficient
export const normalize = normalizeValues

export const createClient = createApiClient
export const api = apiClient

// Utility object for grouped access
export const Utils = {
  // Trading
  trading: {
    positionSize: calculatePositionSize,
    binaryPnL: calculateBinaryOptionPnL,
    winRate: calculateWinRate,
    profitFactor: calculateProfitFactor,
    maxDrawdown: calculateMaxDrawdown,
    sharpeRatio: calculateSharpeRatio,
    kellyCriterion: calculateKellyCriterion,
    cagr: calculateCAGR,
    volatility: calculateVolatility,
    correlation: calculateCorrelation,
    beta: calculateBeta,
    var: calculateVaR,
    expectedShortfall: calculateExpectedShortfall,
    informationRatio: calculateInformationRatio,
    sortinoRatio: calculateSortinoRatio
  },

  // Technical Analysis
  indicators: {
    sma: calculateSMA,
    ema: calculateEMA,
    rsi: calculateRSI,
    macd: calculateMACD,
    bollingerBands: calculateBollingerBands,
    stochastic: calculateStochastic,
    adx: calculateADX,
    williamsR: calculateWilliamsR,
    cci: calculateCCI,
    mfi: calculateMFI,
    parabolicSAR: calculateParabolicSAR
  },

  // Formatting
  format: {
    currency: formatCurrency,
    percentage: formatPercentage,
    compact: formatCompactNumber,
    number: formatNumber,
    pnl: formatPnL,
    price: formatPrice,
    volume: formatVolume,
    marketCap: formatMarketCap,
    spread: formatSpread,
    duration: formatDuration,
    risk: formatRisk,
    winRate: formatWinRate,
    leverage: formatLeverage,
    margin: formatMargin,
    pipValue: formatPipValue,
    orderSize: formatOrderSize,
    accountBalance: formatAccountBalance,

    // Date formatting
    date: formatDate,
    time: formatTime,
    dateTime: formatDateTime,
    relativeTime: formatRelativeTime,
    countdown: formatCountdown,
    tradingSession: formatTradingSession,
    expiryTime: formatExpiryTime,
    marketStatus: formatMarketStatus,
    timeUntilEvent: formatTimeUntilEvent,
    timezoneOffset: formatTimezoneOffset
  },

  // Validation
  validate: {
    email: validateEmail,
    password: validatePassword,
    tradingAmount: validateTradingAmount,
    expiryTime: validateExpiryTime,
    leverage: validateLeverage,
    stopLoss: validateStopLoss,
    takeProfit: validateTakeProfit,
    riskPercentage: validateRiskPercentage,
    phoneNumber: validatePhoneNumber,
    dateOfBirth: validateDateOfBirth,
    required: validateRequired,
    numericRange: validateNumericRange,
    stringLength: validateStringLength,
    url: validateURL,
    fileUpload: validateFileUpload,
    form: validateForm,
    tradingSymbol: validateTradingSymbol,
    apiKey: validateApiKey
  },

  // Statistics
  stats: {
    mean: calculateMean,
    median: calculateMedian,
    mode: calculateMode,
    stdDev: calculateStandardDeviation,
    variance: calculateVariance,
    skewness: calculateSkewness,
    kurtosis: calculateKurtosis,
    percentile: calculatePercentile,
    quartiles: calculateQuartiles,
    iqr: calculateIQR,
    outliers: detectOutliers,
    zScores: calculateZScores,
    normalize: normalizeValues,
    movingAverage: calculateMovingAverage,
    ema: calculateExponentialMovingAverage,
    linearRegression: calculateLinearRegression,
    correlation: calculateCorrelationCoefficient,
    covariance: calculateCovariance,
    confidenceInterval: calculateConfidenceInterval,
    tTest: performTTest,
    rollingStats: calculateRollingStatistics
  },

  // Date utilities
  date: {
    format: formatDate,
    time: formatTime,
    dateTime: formatDateTime,
    relative: formatRelativeTime,
    countdown: formatCountdown,
    tradingSession: formatTradingSession,
    expiry: formatExpiryTime,
    marketStatus: formatMarketStatus,
    timeUntil: formatTimeUntilEvent,
    tradingDay: getTradingDay,
    isWeekend: isWeekend,
    isHoliday: isMarketHoliday,
    nextTradingDay: getNextTradingDay,
    timezoneOffset: formatTimezoneOffset
  },

  // Payload utilities
  payload: {
    format: formatPayload,
    validate: validatePayload,
    sanitize: sanitizePayload
  }
}

// Default export for convenience
export default Utils

// Import all types to ensure they're available globally
import '../types/global'
import '../types/trading'
import '../types/api'
import '../types/components'
