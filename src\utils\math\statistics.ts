// Mathematical and Statistical Utilities
// Advanced mathematical functions for trading analysis

/**
 * Calculate mean (average) of an array of numbers
 */
export const calculateMean = (values: number[]): number => {
  if (values.length === 0) return 0
  return values.reduce((sum, value) => sum + value, 0) / values.length
}

/**
 * Calculate median of an array of numbers
 */
export const calculateMedian = (values: number[]): number => {
  if (values.length === 0) return 0
  
  const sorted = [...values].sort((a, b) => a - b)
  const middle = Math.floor(sorted.length / 2)
  
  if (sorted.length % 2 === 0) {
    return (sorted[middle - 1] + sorted[middle]) / 2
  } else {
    return sorted[middle]
  }
}

/**
 * Calculate mode (most frequent value) of an array of numbers
 */
export const calculateMode = (values: number[]): number[] => {
  if (values.length === 0) return []
  
  const frequency: Record<number, number> = {}
  let maxFreq = 0
  
  for (const value of values) {
    frequency[value] = (frequency[value] || 0) + 1
    maxFreq = Math.max(maxFreq, frequency[value])
  }
  
  return Object.keys(frequency)
    .filter(key => frequency[Number(key)] === maxFreq)
    .map(Number)
}

/**
 * Calculate standard deviation
 */
export const calculateStandardDeviation = (values: number[], sample: boolean = true): number => {
  if (values.length === 0) return 0
  
  const mean = calculateMean(values)
  const squaredDifferences = values.map(value => Math.pow(value - mean, 2))
  const variance = squaredDifferences.reduce((sum, diff) => sum + diff, 0) / (values.length - (sample ? 1 : 0))
  
  return Math.sqrt(variance)
}

/**
 * Calculate variance
 */
export const calculateVariance = (values: number[], sample: boolean = true): number => {
  if (values.length === 0) return 0
  
  const mean = calculateMean(values)
  const squaredDifferences = values.map(value => Math.pow(value - mean, 2))
  
  return squaredDifferences.reduce((sum, diff) => sum + diff, 0) / (values.length - (sample ? 1 : 0))
}

/**
 * Calculate skewness (measure of asymmetry)
 */
export const calculateSkewness = (values: number[]): number => {
  if (values.length < 3) return 0
  
  const mean = calculateMean(values)
  const stdDev = calculateStandardDeviation(values)
  
  if (stdDev === 0) return 0
  
  const n = values.length
  const skewness = values.reduce((sum, value) => {
    return sum + Math.pow((value - mean) / stdDev, 3)
  }, 0)
  
  return (n / ((n - 1) * (n - 2))) * skewness
}

/**
 * Calculate kurtosis (measure of tail heaviness)
 */
export const calculateKurtosis = (values: number[]): number => {
  if (values.length < 4) return 0
  
  const mean = calculateMean(values)
  const stdDev = calculateStandardDeviation(values)
  
  if (stdDev === 0) return 0
  
  const n = values.length
  const kurtosis = values.reduce((sum, value) => {
    return sum + Math.pow((value - mean) / stdDev, 4)
  }, 0)
  
  return ((n * (n + 1)) / ((n - 1) * (n - 2) * (n - 3))) * kurtosis - (3 * Math.pow(n - 1, 2)) / ((n - 2) * (n - 3))
}

/**
 * Calculate percentile
 */
export const calculatePercentile = (values: number[], percentile: number): number => {
  if (values.length === 0) return 0
  if (percentile < 0 || percentile > 100) throw new Error('Percentile must be between 0 and 100')
  
  const sorted = [...values].sort((a, b) => a - b)
  const index = (percentile / 100) * (sorted.length - 1)
  
  if (Number.isInteger(index)) {
    return sorted[index]
  } else {
    const lower = Math.floor(index)
    const upper = Math.ceil(index)
    const weight = index - lower
    
    return sorted[lower] * (1 - weight) + sorted[upper] * weight
  }
}

/**
 * Calculate quartiles (Q1, Q2, Q3)
 */
export const calculateQuartiles = (values: number[]): { q1: number; q2: number; q3: number } => {
  return {
    q1: calculatePercentile(values, 25),
    q2: calculatePercentile(values, 50), // Median
    q3: calculatePercentile(values, 75)
  }
}

/**
 * Calculate interquartile range (IQR)
 */
export const calculateIQR = (values: number[]): number => {
  const quartiles = calculateQuartiles(values)
  return quartiles.q3 - quartiles.q1
}

/**
 * Detect outliers using IQR method
 */
export const detectOutliers = (values: number[]): { outliers: number[]; indices: number[] } => {
  const quartiles = calculateQuartiles(values)
  const iqr = quartiles.q3 - quartiles.q1
  const lowerBound = quartiles.q1 - 1.5 * iqr
  const upperBound = quartiles.q3 + 1.5 * iqr
  
  const outliers: number[] = []
  const indices: number[] = []
  
  values.forEach((value, index) => {
    if (value < lowerBound || value > upperBound) {
      outliers.push(value)
      indices.push(index)
    }
  })
  
  return { outliers, indices }
}

/**
 * Calculate z-score for each value
 */
export const calculateZScores = (values: number[]): number[] => {
  const mean = calculateMean(values)
  const stdDev = calculateStandardDeviation(values)
  
  if (stdDev === 0) return values.map(() => 0)
  
  return values.map(value => (value - mean) / stdDev)
}

/**
 * Normalize values to 0-1 range
 */
export const normalizeValues = (values: number[]): number[] => {
  if (values.length === 0) return []
  
  const min = Math.min(...values)
  const max = Math.max(...values)
  const range = max - min
  
  if (range === 0) return values.map(() => 0)
  
  return values.map(value => (value - min) / range)
}

/**
 * Calculate moving average
 */
export const calculateMovingAverage = (values: number[], window: number): number[] => {
  if (window <= 0 || window > values.length) return []
  
  const result: number[] = []
  
  for (let i = window - 1; i < values.length; i++) {
    const slice = values.slice(i - window + 1, i + 1)
    result.push(calculateMean(slice))
  }
  
  return result
}

/**
 * Calculate exponential moving average
 */
export const calculateExponentialMovingAverage = (values: number[], alpha: number): number[] => {
  if (values.length === 0 || alpha <= 0 || alpha > 1) return []
  
  const result: number[] = [values[0]]
  
  for (let i = 1; i < values.length; i++) {
    const ema = alpha * values[i] + (1 - alpha) * result[i - 1]
    result.push(ema)
  }
  
  return result
}

/**
 * Calculate linear regression
 */
export const calculateLinearRegression = (
  xValues: number[],
  yValues: number[]
): { slope: number; intercept: number; rSquared: number } => {
  if (xValues.length !== yValues.length || xValues.length === 0) {
    throw new Error('X and Y arrays must have the same non-zero length')
  }
  
  const n = xValues.length
  const sumX = xValues.reduce((sum, x) => sum + x, 0)
  const sumY = yValues.reduce((sum, y) => sum + y, 0)
  const sumXY = xValues.reduce((sum, x, i) => sum + x * yValues[i], 0)
  const sumXX = xValues.reduce((sum, x) => sum + x * x, 0)
  const sumYY = yValues.reduce((sum, y) => sum + y * y, 0)
  
  const slope = (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX)
  const intercept = (sumY - slope * sumX) / n
  
  // Calculate R-squared
  const yMean = sumY / n
  const totalSumSquares = yValues.reduce((sum, y) => sum + Math.pow(y - yMean, 2), 0)
  const residualSumSquares = yValues.reduce((sum, y, i) => {
    const predicted = slope * xValues[i] + intercept
    return sum + Math.pow(y - predicted, 2)
  }, 0)
  
  const rSquared = 1 - (residualSumSquares / totalSumSquares)
  
  return { slope, intercept, rSquared }
}

/**
 * Calculate correlation coefficient between two arrays
 */
export const calculateCorrelationCoefficient = (xValues: number[], yValues: number[]): number => {
  if (xValues.length !== yValues.length || xValues.length === 0) {
    throw new Error('X and Y arrays must have the same non-zero length')
  }
  
  const n = xValues.length
  const sumX = xValues.reduce((sum, x) => sum + x, 0)
  const sumY = yValues.reduce((sum, y) => sum + y, 0)
  const sumXY = xValues.reduce((sum, x, i) => sum + x * yValues[i], 0)
  const sumXX = xValues.reduce((sum, x) => sum + x * x, 0)
  const sumYY = yValues.reduce((sum, y) => sum + y * y, 0)
  
  const numerator = n * sumXY - sumX * sumY
  const denominator = Math.sqrt((n * sumXX - sumX * sumX) * (n * sumYY - sumY * sumY))
  
  return denominator === 0 ? 0 : numerator / denominator
}

/**
 * Calculate covariance between two arrays
 */
export const calculateCovariance = (xValues: number[], yValues: number[], sample: boolean = true): number => {
  if (xValues.length !== yValues.length || xValues.length === 0) {
    throw new Error('X and Y arrays must have the same non-zero length')
  }
  
  const xMean = calculateMean(xValues)
  const yMean = calculateMean(yValues)
  
  const covariance = xValues.reduce((sum, x, i) => {
    return sum + (x - xMean) * (yValues[i] - yMean)
  }, 0)
  
  return covariance / (xValues.length - (sample ? 1 : 0))
}

/**
 * Calculate confidence interval for mean
 */
export const calculateConfidenceInterval = (
  values: number[],
  confidenceLevel: number = 0.95
): { lower: number; upper: number; margin: number } => {
  if (values.length === 0) return { lower: 0, upper: 0, margin: 0 }
  
  const mean = calculateMean(values)
  const stdDev = calculateStandardDeviation(values)
  const n = values.length
  
  // Use t-distribution for small samples, normal for large samples
  const degreesOfFreedom = n - 1
  const alpha = 1 - confidenceLevel
  
  // Simplified t-value approximation (for more accuracy, use a proper t-table)
  const tValue = n >= 30 ? 1.96 : 2.0 // Rough approximation
  
  const standardError = stdDev / Math.sqrt(n)
  const margin = tValue * standardError
  
  return {
    lower: mean - margin,
    upper: mean + margin,
    margin
  }
}

/**
 * Perform one-sample t-test
 */
export const performTTest = (
  values: number[],
  hypothesizedMean: number = 0
): { tStatistic: number; pValue: number; degreesOfFreedom: number } => {
  if (values.length === 0) return { tStatistic: 0, pValue: 1, degreesOfFreedom: 0 }
  
  const sampleMean = calculateMean(values)
  const sampleStdDev = calculateStandardDeviation(values)
  const n = values.length
  const degreesOfFreedom = n - 1
  
  const tStatistic = (sampleMean - hypothesizedMean) / (sampleStdDev / Math.sqrt(n))
  
  // Simplified p-value calculation (for more accuracy, use a proper t-distribution)
  const pValue = Math.abs(tStatistic) > 2 ? 0.05 : 0.1 // Very rough approximation
  
  return { tStatistic, pValue, degreesOfFreedom }
}

/**
 * Calculate rolling statistics
 */
export const calculateRollingStatistics = (
  values: number[],
  window: number
): Array<{ mean: number; stdDev: number; min: number; max: number }> => {
  if (window <= 0 || window > values.length) return []
  
  const result: Array<{ mean: number; stdDev: number; min: number; max: number }> = []
  
  for (let i = window - 1; i < values.length; i++) {
    const slice = values.slice(i - window + 1, i + 1)
    
    result.push({
      mean: calculateMean(slice),
      stdDev: calculateStandardDeviation(slice),
      min: Math.min(...slice),
      max: Math.max(...slice)
    })
  }
  
  return result
}
