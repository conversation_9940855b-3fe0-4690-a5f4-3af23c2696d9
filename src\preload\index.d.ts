declare global {
  interface Window {
    electron: {
      ipcRenderer: {
        send: (channel: string, ...args: unknown[]) => void
        invoke: <T = unknown>(channel: string, ...args: unknown[]) => Promise<T>
        on: (channel: string, func: (...args: unknown[]) => void) => () => void
        once: (channel: string, func: (...args: unknown[]) => void) => void
        removeAllListeners: (channel: string) => void
      }
    }
    api: {
      invoke: <T = unknown>(channel: string, ...args: unknown[]) => Promise<T>
      send: (channel: string, ...args: unknown[]) => void
      on: (channel: string, func: (...args: unknown[]) => void) => () => void
    }
  }
}
