// Technical Indicator Utilities
// Implementation of common technical analysis indicators

/**
 * Calculate Simple Moving Average (SMA)
 */
export const calculateSMA = (prices: number[], period: number): number[] => {
  if (prices.length < period) return []
  
  const sma: number[] = []
  
  for (let i = period - 1; i < prices.length; i++) {
    const sum = prices.slice(i - period + 1, i + 1).reduce((acc, price) => acc + price, 0)
    sma.push(sum / period)
  }
  
  return sma
}

/**
 * Calculate Exponential Moving Average (EMA)
 */
export const calculateEMA = (prices: number[], period: number): number[] => {
  if (prices.length === 0) return []
  
  const ema: number[] = []
  const multiplier = 2 / (period + 1)
  
  // First EMA is SMA
  ema[0] = prices[0]
  
  for (let i = 1; i < prices.length; i++) {
    ema[i] = (prices[i] - ema[i - 1]) * multiplier + ema[i - 1]
  }
  
  return ema
}

/**
 * Calculate Relative Strength Index (RSI)
 */
export const calculateRSI = (prices: number[], period: number = 14): number[] => {
  if (prices.length < period + 1) return []
  
  const rsi: number[] = []
  const gains: number[] = []
  const losses: number[] = []
  
  // Calculate price changes
  for (let i = 1; i < prices.length; i++) {
    const change = prices[i] - prices[i - 1]
    gains.push(change > 0 ? change : 0)
    losses.push(change < 0 ? Math.abs(change) : 0)
  }
  
  // Calculate initial average gain and loss
  let avgGain = gains.slice(0, period).reduce((sum, gain) => sum + gain, 0) / period
  let avgLoss = losses.slice(0, period).reduce((sum, loss) => sum + loss, 0) / period
  
  // Calculate RSI
  for (let i = period; i < gains.length; i++) {
    avgGain = (avgGain * (period - 1) + gains[i]) / period
    avgLoss = (avgLoss * (period - 1) + losses[i]) / period
    
    const rs = avgLoss === 0 ? 100 : avgGain / avgLoss
    rsi.push(100 - (100 / (1 + rs)))
  }
  
  return rsi
}

/**
 * Calculate MACD (Moving Average Convergence Divergence)
 */
export const calculateMACD = (
  prices: number[],
  fastPeriod: number = 12,
  slowPeriod: number = 26,
  signalPeriod: number = 9
): { macd: number[]; signal: number[]; histogram: number[] } => {
  const fastEMA = calculateEMA(prices, fastPeriod)
  const slowEMA = calculateEMA(prices, slowPeriod)
  
  // Align arrays (slow EMA starts later)
  const startIndex = slowPeriod - fastPeriod
  const alignedFastEMA = fastEMA.slice(startIndex)
  
  // Calculate MACD line
  const macd = alignedFastEMA.map((fast, i) => fast - slowEMA[i])
  
  // Calculate signal line (EMA of MACD)
  const signal = calculateEMA(macd, signalPeriod)
  
  // Calculate histogram (MACD - Signal)
  const histogramStartIndex = signalPeriod - 1
  const histogram = macd.slice(histogramStartIndex).map((macdValue, i) => macdValue - signal[i])
  
  return { macd, signal, histogram }
}

/**
 * Calculate Bollinger Bands
 */
export const calculateBollingerBands = (
  prices: number[],
  period: number = 20,
  standardDeviations: number = 2
): { upper: number[]; middle: number[]; lower: number[] } => {
  const sma = calculateSMA(prices, period)
  const upper: number[] = []
  const lower: number[] = []
  
  for (let i = 0; i < sma.length; i++) {
    const dataIndex = i + period - 1
    const slice = prices.slice(dataIndex - period + 1, dataIndex + 1)
    
    // Calculate standard deviation
    const mean = sma[i]
    const variance = slice.reduce((sum, price) => sum + Math.pow(price - mean, 2), 0) / period
    const stdDev = Math.sqrt(variance)
    
    upper.push(mean + (standardDeviations * stdDev))
    lower.push(mean - (standardDeviations * stdDev))
  }
  
  return { upper, middle: sma, lower }
}

/**
 * Calculate Stochastic Oscillator
 */
export const calculateStochastic = (
  highs: number[],
  lows: number[],
  closes: number[],
  kPeriod: number = 14,
  dPeriod: number = 3
): { k: number[]; d: number[] } => {
  if (highs.length !== lows.length || lows.length !== closes.length) {
    throw new Error('High, low, and close arrays must have the same length')
  }
  
  const k: number[] = []
  
  for (let i = kPeriod - 1; i < closes.length; i++) {
    const periodHighs = highs.slice(i - kPeriod + 1, i + 1)
    const periodLows = lows.slice(i - kPeriod + 1, i + 1)
    
    const highestHigh = Math.max(...periodHighs)
    const lowestLow = Math.min(...periodLows)
    
    const kValue = ((closes[i] - lowestLow) / (highestHigh - lowestLow)) * 100
    k.push(kValue)
  }
  
  // Calculate %D (SMA of %K)
  const d = calculateSMA(k, dPeriod)
  
  return { k, d }
}

/**
 * Calculate Average Directional Index (ADX)
 */
export const calculateADX = (
  highs: number[],
  lows: number[],
  closes: number[],
  period: number = 14
): { adx: number[]; plusDI: number[]; minusDI: number[] } => {
  if (highs.length !== lows.length || lows.length !== closes.length) {
    throw new Error('High, low, and close arrays must have the same length')
  }
  
  const trueRanges: number[] = []
  const plusDMs: number[] = []
  const minusDMs: number[] = []
  
  // Calculate True Range and Directional Movements
  for (let i = 1; i < closes.length; i++) {
    const high = highs[i]
    const low = lows[i]
    const close = closes[i]
    const prevClose = closes[i - 1]
    const prevHigh = highs[i - 1]
    const prevLow = lows[i - 1]
    
    // True Range
    const tr = Math.max(
      high - low,
      Math.abs(high - prevClose),
      Math.abs(low - prevClose)
    )
    trueRanges.push(tr)
    
    // Directional Movements
    const plusDM = high - prevHigh > prevLow - low ? Math.max(high - prevHigh, 0) : 0
    const minusDM = prevLow - low > high - prevHigh ? Math.max(prevLow - low, 0) : 0
    
    plusDMs.push(plusDM)
    minusDMs.push(minusDM)
  }
  
  // Calculate smoothed values
  const smoothedTR = calculateEMA(trueRanges, period)
  const smoothedPlusDM = calculateEMA(plusDMs, period)
  const smoothedMinusDM = calculateEMA(minusDMs, period)
  
  // Calculate DI values
  const plusDI = smoothedPlusDM.map((dm, i) => (dm / smoothedTR[i]) * 100)
  const minusDI = smoothedMinusDM.map((dm, i) => (dm / smoothedTR[i]) * 100)
  
  // Calculate DX and ADX
  const dx = plusDI.map((plus, i) => {
    const minus = minusDI[i]
    const sum = plus + minus
    return sum === 0 ? 0 : (Math.abs(plus - minus) / sum) * 100
  })
  
  const adx = calculateEMA(dx, period)
  
  return { adx, plusDI, minusDI }
}

/**
 * Calculate Williams %R
 */
export const calculateWilliamsR = (
  highs: number[],
  lows: number[],
  closes: number[],
  period: number = 14
): number[] => {
  if (highs.length !== lows.length || lows.length !== closes.length) {
    throw new Error('High, low, and close arrays must have the same length')
  }
  
  const williamsR: number[] = []
  
  for (let i = period - 1; i < closes.length; i++) {
    const periodHighs = highs.slice(i - period + 1, i + 1)
    const periodLows = lows.slice(i - period + 1, i + 1)
    
    const highestHigh = Math.max(...periodHighs)
    const lowestLow = Math.min(...periodLows)
    
    const wr = ((highestHigh - closes[i]) / (highestHigh - lowestLow)) * -100
    williamsR.push(wr)
  }
  
  return williamsR
}

/**
 * Calculate Commodity Channel Index (CCI)
 */
export const calculateCCI = (
  highs: number[],
  lows: number[],
  closes: number[],
  period: number = 20
): number[] => {
  if (highs.length !== lows.length || lows.length !== closes.length) {
    throw new Error('High, low, and close arrays must have the same length')
  }
  
  // Calculate Typical Price
  const typicalPrices = highs.map((high, i) => (high + lows[i] + closes[i]) / 3)
  
  // Calculate SMA of Typical Price
  const smaTP = calculateSMA(typicalPrices, period)
  
  const cci: number[] = []
  
  for (let i = 0; i < smaTP.length; i++) {
    const dataIndex = i + period - 1
    const slice = typicalPrices.slice(dataIndex - period + 1, dataIndex + 1)
    
    // Calculate Mean Deviation
    const meanDeviation = slice.reduce((sum, tp) => sum + Math.abs(tp - smaTP[i]), 0) / period
    
    const cciValue = meanDeviation === 0 ? 0 : (typicalPrices[dataIndex] - smaTP[i]) / (0.015 * meanDeviation)
    cci.push(cciValue)
  }
  
  return cci
}

/**
 * Calculate Money Flow Index (MFI)
 */
export const calculateMFI = (
  highs: number[],
  lows: number[],
  closes: number[],
  volumes: number[],
  period: number = 14
): number[] => {
  if (highs.length !== lows.length || lows.length !== closes.length || closes.length !== volumes.length) {
    throw new Error('All arrays must have the same length')
  }
  
  const typicalPrices = highs.map((high, i) => (high + lows[i] + closes[i]) / 3)
  const rawMoneyFlows = typicalPrices.map((tp, i) => tp * volumes[i])
  
  const mfi: number[] = []
  
  for (let i = period; i < typicalPrices.length; i++) {
    let positiveFlow = 0
    let negativeFlow = 0
    
    for (let j = i - period + 1; j <= i; j++) {
      if (typicalPrices[j] > typicalPrices[j - 1]) {
        positiveFlow += rawMoneyFlows[j]
      } else if (typicalPrices[j] < typicalPrices[j - 1]) {
        negativeFlow += rawMoneyFlows[j]
      }
    }
    
    const moneyFlowRatio = negativeFlow === 0 ? 100 : positiveFlow / negativeFlow
    const mfiValue = 100 - (100 / (1 + moneyFlowRatio))
    mfi.push(mfiValue)
  }
  
  return mfi
}

/**
 * Calculate Parabolic SAR
 */
export const calculateParabolicSAR = (
  highs: number[],
  lows: number[],
  accelerationFactor: number = 0.02,
  maxAcceleration: number = 0.2
): number[] => {
  if (highs.length !== lows.length || highs.length < 2) {
    return []
  }
  
  const sar: number[] = []
  let isUptrend = highs[1] > highs[0]
  let af = accelerationFactor
  let extremePoint = isUptrend ? highs[1] : lows[1]
  
  sar[0] = isUptrend ? lows[0] : highs[0]
  sar[1] = sar[0] + af * (extremePoint - sar[0])
  
  for (let i = 2; i < highs.length; i++) {
    const prevSAR = sar[i - 1]
    let newSAR = prevSAR + af * (extremePoint - prevSAR)
    
    // Check for trend reversal
    if (isUptrend) {
      if (lows[i] <= newSAR) {
        // Trend reversal to downtrend
        isUptrend = false
        newSAR = extremePoint
        extremePoint = lows[i]
        af = accelerationFactor
      } else {
        // Continue uptrend
        if (highs[i] > extremePoint) {
          extremePoint = highs[i]
          af = Math.min(af + accelerationFactor, maxAcceleration)
        }
        // Ensure SAR doesn't exceed previous two lows
        newSAR = Math.min(newSAR, lows[i - 1], lows[i - 2])
      }
    } else {
      if (highs[i] >= newSAR) {
        // Trend reversal to uptrend
        isUptrend = true
        newSAR = extremePoint
        extremePoint = highs[i]
        af = accelerationFactor
      } else {
        // Continue downtrend
        if (lows[i] < extremePoint) {
          extremePoint = lows[i]
          af = Math.min(af + accelerationFactor, maxAcceleration)
        }
        // Ensure SAR doesn't exceed previous two highs
        newSAR = Math.max(newSAR, highs[i - 1], highs[i - 2])
      }
    }
    
    sar[i] = newSAR
  }
  
  return sar
}
