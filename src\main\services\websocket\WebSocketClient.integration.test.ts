import { describe, it, expect, beforeEach, afterEach, vi, Mock } from 'vitest'
import { WSConnectionState } from '../../../types/websocket/enums'
import WebSocketClient from './WebSocketClient'
import { io } from 'socket.io-client'
import { BrowserWindow } from 'electron'

// Mock dependencies
vi.mock('socket.io-client')
vi.mock('electron', () => {
  const mockBrowserWindow = {
    getAllWindows: vi.fn(() => [])
  }
  return {
    BrowserWindow: mockBrowserWindow
  }
})
vi.mock('./Auth', () => ({
  default: {
    getInstance: vi.fn(() => ({
      getAuthPayload: vi.fn(() => ({
        session: 'test-session',
        isDemo: 1,
        uid: 12345,
        platform: 0,
        isFastHistory: false
      }))
    }))
  }
}))
vi.mock('../../../utils/payload', () => ({
  formatPayload: vi.fn((data) => data)
}))

describe('WebSocketClient Integration Tests', () => {
  let client: WebSocketClient
  let mockSocket: any
  let mockBrowserWindow: any

  beforeEach(() => {
    vi.clearAllMocks()

    mockSocket = {
      on: vi.fn(),
      once: vi.fn(),
      emit: vi.fn(),
      disconnect: vi.fn(),
      removeAllListeners: vi.fn(),
      onAny: vi.fn(),
      connected: true
    }
    ;(io as Mock).mockReturnValue(mockSocket)

    mockBrowserWindow = {
      webContents: {
        send: vi.fn()
      }
    }
    ;(BrowserWindow.getAllWindows as Mock).mockReturnValue([mockBrowserWindow])

    client = WebSocketClient.getInstance()
  })

  afterEach(() => {
    if (client) {
      client.disconnect()
    }
  })

  describe('Complete Connection Lifecycle', () => {
    it('should handle full connection flow', async () => {
      // Start connection
      client.connect()
      expect(client.getConnectionStatus()).toBe(WSConnectionState.CONNECTING)

      // Simulate socket connect event
      const connectHandler = mockSocket.on.mock.calls.find((call) => call[0] === 'connect')[1]
      connectHandler()

      // Should emit auth
      expect(mockSocket.emit).toHaveBeenCalledWith('auth', expect.any(Object))

      // Simulate successful authentication
      const authHandler = mockSocket.on.mock.calls.find((call) => call[0] === 'successauth')[1]
      authHandler()

      expect(client.getConnectionStatus()).toBe(WSConnectionState.CONNECTED)
      expect(client.isConnected()).toBe(true)
    })

    it('should handle connection with pending listeners', async () => {
      const balanceListener = vi.fn()
      const assetsListener = vi.fn()

      // Add listeners while disconnected
      client.on('successupdateBalance', balanceListener)
      client.on('updateAssets', assetsListener)

      // Connect
      client.connect()
      const connectHandler = mockSocket.on.mock.calls.find((call) => call[0] === 'connect')[1]
      connectHandler()

      const authHandler = mockSocket.on.mock.calls.find((call) => call[0] === 'successauth')[1]
      authHandler()

      // Verify listeners were attached
      expect(mockSocket.on).toHaveBeenCalledWith('successupdateBalance', expect.any(Function))
      expect(mockSocket.on).toHaveBeenCalledWith('updateAssets', expect.any(Function))
    })

    it('should handle connection with buffered events', async () => {
      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {})

      // Send events while disconnected (should be buffered)
      client.send('placeOrder', { amount: 100 })
      client.send('updateSettings', { theme: 'dark' })

      // Connect
      client.connect()
      const connectHandler = mockSocket.on.mock.calls.find((call) => call[0] === 'connect')[1]
      connectHandler()

      const authHandler = mockSocket.on.mock.calls.find((call) => call[0] === 'successauth')[1]
      authHandler()

      // Buffered events should be sent after connection
      expect(mockSocket.emit).toHaveBeenCalledWith('placeOrder', { amount: 100 })
      expect(mockSocket.emit).toHaveBeenCalledWith('updateSettings', { theme: 'dark' })

      consoleSpy.mockRestore()
    })
  })

  describe('Reconnection Scenarios', () => {
    it('should handle unexpected disconnection and reconnect', async () => {
      vi.useFakeTimers()

      // Establish connection
      client.connect()
      const connectHandler = mockSocket.on.mock.calls.find((call) => call[0] === 'connect')[1]
      connectHandler()

      const authHandler = mockSocket.on.mock.calls.find((call) => call[0] === 'successauth')[1]
      authHandler()

      expect(client.isConnected()).toBe(true)

      // Simulate unexpected disconnect
      const disconnectHandler = mockSocket.on.mock.calls.find((call) => call[0] === 'disconnect')[1]
      disconnectHandler('transport close')

      expect(client.getConnectionStatus()).toBe(WSConnectionState.RECONNECTING)

      // Fast forward to trigger reconnection
      vi.advanceTimersByTime(1000)

      // Should attempt to reconnect
      expect(io).toHaveBeenCalledTimes(2) // Initial + reconnect

      vi.useRealTimers()
    })

    it('should handle connection errors with retry', async () => {
      vi.useFakeTimers()

      client.connect()

      // Simulate connection error
      const errorHandler = mockSocket.on.mock.calls.find((call) => call[0] === 'connect_error')[1]
      errorHandler(new Error('Network error'))

      expect(client.getConnectionStatus()).toBe(WSConnectionState.RECONNECTING)

      // Should schedule reconnection
      vi.advanceTimersByTime(1000)
      expect(io).toHaveBeenCalledTimes(2)

      vi.useRealTimers()
    })

    it('should stop reconnecting after max attempts', async () => {
      vi.useFakeTimers()

      client.connect()

      // Simulate multiple connection failures
      const errorHandler = mockSocket.on.mock.calls.find((call) => call[0] === 'connect_error')[1]

      // Exceed max reconnection attempts
      for (let i = 0; i < 6; i++) {
        errorHandler(new Error('Connection failed'))
        vi.advanceTimersByTime(Math.pow(2, i) * 1000)
      }

      expect(client.getConnectionStatus()).toBe(WSConnectionState.ERROR)

      vi.useRealTimers()
    })
  })

  describe('Event Processing Integration', () => {
    it('should process real-time balance updates', async () => {
      const balanceListener = vi.fn()
      client.on('successupdateBalance', balanceListener)

      // Establish connection
      client.connect()
      const connectHandler = mockSocket.on.mock.calls.find((call) => call[0] === 'connect')[1]
      connectHandler()

      const authHandler = mockSocket.on.mock.calls.find((call) => call[0] === 'successauth')[1]
      authHandler()

      // Simulate balance update from server
      const balanceHandler = mockSocket.on.mock.calls.find(
        (call) => call[0] === 'successupdateBalance'
      )[1]

      const balanceData = { balance: 1000, isDemo: 1 }
      balanceHandler(balanceData)

      expect(balanceListener).toHaveBeenCalledWith(balanceData)
      expect(client.getAccountBalance()).toEqual(balanceData)
    })

    it('should broadcast events to renderer process', async () => {
      // Establish connection
      client.connect()
      const connectHandler = mockSocket.on.mock.calls.find((call) => call[0] === 'connect')[1]
      connectHandler()

      const authHandler = mockSocket.on.mock.calls.find((call) => call[0] === 'successauth')[1]
      authHandler()

      // Should broadcast connected event
      expect(mockBrowserWindow.webContents.send).toHaveBeenCalledWith(
        'ws:event',
        'connected',
        undefined
      )
    })

    it('should handle heartbeat with latency calculation', async () => {
      vi.useFakeTimers()

      // Establish connection
      client.connect()
      const connectHandler = mockSocket.on.mock.calls.find((call) => call[0] === 'connect')[1]
      connectHandler()

      const authHandler = mockSocket.on.mock.calls.find((call) => call[0] === 'successauth')[1]
      authHandler()

      // Fast forward to trigger heartbeat
      vi.advanceTimersByTime(20000)

      // Should send heartbeat
      expect(mockSocket.emit).toHaveBeenCalledWith('ps')

      // Simulate heartbeat response
      const heartbeatHandler = mockSocket.on.mock.calls.find((call) => call[0] === 'ps')[1]
      vi.advanceTimersByTime(100) // 100ms latency
      heartbeatHandler()

      const metrics = client.getConnectionMetrics()
      expect(metrics.averageLatency).toBeGreaterThan(0)

      vi.useRealTimers()
    })
  })

  describe('Error Recovery Integration', () => {
    it('should recover from temporary network issues', async () => {
      vi.useFakeTimers()

      // Establish connection
      client.connect()
      const connectHandler = mockSocket.on.mock.calls.find((call) => call[0] === 'connect')[1]
      connectHandler()

      const authHandler = mockSocket.on.mock.calls.find((call) => call[0] === 'successauth')[1]
      authHandler()

      // Add event listener and buffer some events
      const listener = vi.fn()
      client.on('successupdateBalance', listener)
      client.send('testEvent', { data: 'test' })

      // Simulate network disconnection
      const disconnectHandler = mockSocket.on.mock.calls.find((call) => call[0] === 'disconnect')[1]
      disconnectHandler('transport close')

      // Send more events while disconnected (should be buffered)
      client.send('bufferedEvent', { data: 'buffered' })

      // Simulate reconnection
      vi.advanceTimersByTime(1000)

      // Simulate successful reconnection
      const newConnectHandler = mockSocket.on.mock.calls.find((call) => call[0] === 'connect')[1]
      newConnectHandler()

      const newAuthHandler = mockSocket.on.mock.calls.find((call) => call[0] === 'successauth')[1]
      newAuthHandler()

      // Should process buffered events
      expect(mockSocket.emit).toHaveBeenCalledWith('bufferedEvent', { data: 'buffered' })

      vi.useRealTimers()
    })

    it('should handle authentication failures', async () => {
      client.connect()
      const connectHandler = mockSocket.on.mock.calls.find((call) => call[0] === 'connect')[1]
      connectHandler()

      // Simulate auth failure by not calling successauth
      // Instead simulate disconnect due to auth failure
      const disconnectHandler = mockSocket.on.mock.calls.find((call) => call[0] === 'disconnect')[1]
      disconnectHandler('authentication failed')

      expect(client.getConnectionStatus()).toBe(WSConnectionState.RECONNECTING)
    })
  })

  describe('Health Monitoring Integration', () => {
    it('should detect and report connection health issues', async () => {
      vi.useFakeTimers()

      // Establish connection
      client.connect()
      const connectHandler = mockSocket.on.mock.calls.find((call) => call[0] === 'connect')[1]
      connectHandler()

      const authHandler = mockSocket.on.mock.calls.find((call) => call[0] === 'successauth')[1]
      authHandler()

      // Fast forward past heartbeat timeout
      vi.advanceTimersByTime(70000) // 70 seconds (past 60s timeout)

      const health = client.getHealthStatus()
      expect(health.isHealthy).toBe(false)
      expect(health.issues).toContain('Heartbeat timeout detected')

      vi.useRealTimers()
    })

    it('should broadcast health status to renderer', async () => {
      vi.useFakeTimers()

      // Establish connection
      client.connect()
      const connectHandler = mockSocket.on.mock.calls.find((call) => call[0] === 'connect')[1]
      connectHandler()

      const authHandler = mockSocket.on.mock.calls.find((call) => call[0] === 'successauth')[1]
      authHandler()

      // Fast forward to trigger health check with issues
      vi.advanceTimersByTime(70000)

      // Should broadcast health status
      expect(mockBrowserWindow.webContents.send).toHaveBeenCalledWith(
        'ws:event',
        'ws:health',
        expect.objectContaining({
          isHealthy: false,
          issues: expect.arrayContaining(['Heartbeat timeout detected'])
        })
      )

      vi.useRealTimers()
    })
  })
})
