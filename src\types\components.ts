// React Component Prop Types
// Comprehensive type definitions for all UI components

import { ReactN<PERSON>, MouseE<PERSON>, ChangeEvent, FormEvent } from 'react'

export interface IconProps extends BaseComponentProps {
  name: string
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl'
  color?: string
  spin?: boolean
}

export interface InputProps extends BaseComponentProps {
  type?: 'text' | 'email' | 'password' | 'number' | 'tel' | 'url'
  value?: string | number
  placeholder?: string
  disabled?: boolean
  required?: boolean
  error?: string
  label?: string
  hint?: string
  onChange?: (event: ChangeEvent<HTMLInputElement>) => void
  onBlur?: (event: ChangeEvent<HTMLInputElement>) => void
  onFocus?: (event: ChangeEvent<HTMLInputElement>) => void
}

export interface SelectProps extends BaseComponentProps {
  value?: string | number
  placeholder?: string
  disabled?: boolean
  required?: boolean
  error?: string
  label?: string
  options: SelectOption[]
  onChange?: (value: string | number) => void
}

export interface SelectOption {
  value: string | number
  label: string
  disabled?: boolean
  group?: string
}

export interface CheckboxProps extends BaseComponentProps {
  checked?: boolean
  disabled?: boolean
  label?: string
  error?: string
  onChange?: (checked: boolean) => void
}

export interface RadioProps extends BaseComponentProps {
  name: string
  value: string | number
  checked?: boolean
  disabled?: boolean
  label?: string
  onChange?: (value: string | number) => void
}

export interface FormProps extends BaseComponentProps {
  onSubmit?: (event: FormEvent<HTMLFormElement>) => void
  noValidate?: boolean
}

export interface CardProps extends BaseComponentProps {
  title?: string
  subtitle?: string
  footer?: ReactNode
  padding?: 'none' | 'sm' | 'md' | 'lg'
  shadow?: 'none' | 'sm' | 'md' | 'lg'
  border?: boolean
}

export interface TableProps<T = unknown> extends BaseComponentProps {
  data: T[]
  columns: TableColumn<T>[]
  loading?: boolean
  pagination?: PaginationConfig
  sortable?: boolean
  selectable?: boolean
  onRowClick?: (row: T, index: number) => void
  onSelectionChange?: (selectedRows: T[]) => void
}

export interface TableColumn<T = unknown> {
  key: keyof T | string
  title: string
  width?: string | number
  sortable?: boolean
  render?: (value: unknown, row: T, index: number) => ReactNode
  align?: 'left' | 'center' | 'right'
}

export interface PaginationConfig {
  current: number
  total: number
  pageSize: number
  showSizeChanger?: boolean
  showQuickJumper?: boolean
  onChange?: (page: number, pageSize: number) => void
}

export interface TabsProps extends BaseComponentProps {
  activeKey?: string
  defaultActiveKey?: string
  items: TabItem[]
  onChange?: (key: string) => void
  size?: 'sm' | 'md' | 'lg'
  type?: 'line' | 'card'
}

export interface TabItem {
  key: string
  label: string
  content: ReactNode
  disabled?: boolean
  closable?: boolean
}

export interface DrawerProps extends BaseComponentProps {
  isOpen: boolean
  onClose: () => void
  title?: string
  placement?: 'left' | 'right' | 'top' | 'bottom'
  size?: 'sm' | 'md' | 'lg' | 'xl'
  footer?: ReactNode
  closable?: boolean
  maskClosable?: boolean
}

export interface TooltipProps extends BaseComponentProps {
  content: ReactNode
  placement?: 'top' | 'bottom' | 'left' | 'right'
  trigger?: 'hover' | 'click' | 'focus'
  delay?: number
}

export interface PopoverProps extends BaseComponentProps {
  content: ReactNode
  title?: string
  placement?: 'top' | 'bottom' | 'left' | 'right'
  trigger?: 'hover' | 'click' | 'focus'
  visible?: boolean
  onVisibleChange?: (visible: boolean) => void
}

export interface DropdownProps extends BaseComponentProps {
  items: DropdownItem[]
  trigger?: 'hover' | 'click'
  placement?: 'bottom' | 'bottomLeft' | 'bottomRight' | 'top' | 'topLeft' | 'topRight'
  onSelect?: (key: string) => void
}

export interface DropdownItem {
  key: string
  label: string
  icon?: string
  disabled?: boolean
  danger?: boolean
  divider?: boolean
}

export interface AlertProps extends BaseComponentProps {
  type?: 'info' | 'success' | 'warning' | 'error'
  title?: string
  message: string
  closable?: boolean
  showIcon?: boolean
  onClose?: () => void
}

export interface NotificationProps {
  id: string
  type: 'info' | 'success' | 'warning' | 'error'
  title: string
  message: string
  duration?: number
  closable?: boolean
  onClose?: (id: string) => void
}

export interface LoadingProps extends BaseComponentProps {
  spinning?: boolean
  size?: 'sm' | 'md' | 'lg'
  tip?: string
}

export interface ProgressProps extends BaseComponentProps {
  percent: number
  status?: 'normal' | 'active' | 'exception' | 'success'
  showInfo?: boolean
  size?: 'sm' | 'md' | 'lg'
  strokeColor?: string
}

export interface AvatarProps extends BaseComponentProps {
  src?: string
  alt?: string
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl'
  shape?: 'circle' | 'square'
  icon?: string
  name?: string
}

export interface BadgeProps extends BaseComponentProps {
  count?: number
  dot?: boolean
  status?: 'default' | 'processing' | 'success' | 'error' | 'warning'
  color?: string
  text?: string
}

export interface TagProps extends BaseComponentProps {
  color?: string
  closable?: boolean
  onClose?: () => void
}

export interface StepsProps extends BaseComponentProps {
  current: number
  items: StepItem[]
  direction?: 'horizontal' | 'vertical'
  size?: 'sm' | 'md'
  status?: 'wait' | 'process' | 'finish' | 'error'
  onChange?: (current: number) => void
}

export interface StepItem {
  title: string
  description?: string
  icon?: string
  status?: 'wait' | 'process' | 'finish' | 'error'
}

export interface BreadcrumbProps extends BaseComponentProps {
  items: BreadcrumbItem[]
  separator?: string
}

export interface BreadcrumbItem {
  title: string
  href?: string
  onClick?: () => void
}

export interface MenuProps extends BaseComponentProps {
  items: MenuItem[]
  mode?: 'horizontal' | 'vertical' | 'inline'
  theme?: 'light' | 'dark'
  selectedKeys?: string[]
  openKeys?: string[]
  onSelect?: (keys: string[]) => void
  onOpenChange?: (keys: string[]) => void
}

export interface MenuItem {
  key: string
  label: string
  icon?: string
  disabled?: boolean
  children?: MenuItem[]
  type?: 'item' | 'group' | 'divider'
}

export interface DatePickerProps extends BaseComponentProps {
  value?: Date
  placeholder?: string
  disabled?: boolean
  format?: string
  showTime?: boolean
  disabledDate?: (date: Date) => boolean
  onChange?: (date: Date | null) => void
}

export interface TimePickerProps extends BaseComponentProps {
  value?: Date
  placeholder?: string
  disabled?: boolean
  format?: string
  hourStep?: number
  minuteStep?: number
  secondStep?: number
  onChange?: (time: Date | null) => void
}

export interface SliderProps extends BaseComponentProps {
  value?: number | [number, number]
  min?: number
  max?: number
  step?: number
  marks?: Record<number, string>
  range?: boolean
  disabled?: boolean
  vertical?: boolean
  onChange?: (value: number | [number, number]) => void
}

export interface SwitchProps extends BaseComponentProps {
  checked?: boolean
  disabled?: boolean
  size?: 'sm' | 'md'
  checkedChildren?: ReactNode
  unCheckedChildren?: ReactNode
  onChange?: (checked: boolean) => void
}

export interface RateProps extends BaseComponentProps {
  value?: number
  count?: number
  allowHalf?: boolean
  allowClear?: boolean
  disabled?: boolean
  character?: ReactNode
  onChange?: (value: number) => void
}

export interface TransferProps<T = unknown> extends BaseComponentProps {
  dataSource: T[]
  targetKeys: string[]
  selectedKeys?: string[]
  render?: (item: T) => ReactNode
  showSearch?: boolean
  searchPlaceholder?: string
  onChange?: (targetKeys: string[], direction: 'left' | 'right', moveKeys: string[]) => void
  onSelectChange?: (sourceSelectedKeys: string[], targetSelectedKeys: string[]) => void
}

export interface UploadProps extends BaseComponentProps {
  action?: string
  accept?: string
  multiple?: boolean
  disabled?: boolean
  maxCount?: number
  maxSize?: number
  beforeUpload?: (file: File) => boolean | Promise<boolean>
  onChange?: (fileList: File[]) => void
  onRemove?: (file: File) => void
}

export interface TreeProps<T = unknown> extends BaseComponentProps {
  data: TreeNode<T>[]
  checkable?: boolean
  selectable?: boolean
  multiple?: boolean
  expandedKeys?: string[]
  selectedKeys?: string[]
  checkedKeys?: string[]
  onExpand?: (expandedKeys: string[]) => void
  onSelect?: (selectedKeys: string[], info: { node: TreeNode<T> }) => void
  onCheck?: (checkedKeys: string[], info: { node: TreeNode<T> }) => void
}

export interface TreeNode<T = unknown> {
  key: string
  title: string
  children?: TreeNode<T>[]
  disabled?: boolean
  checkable?: boolean
  selectable?: boolean
  data?: T
}
