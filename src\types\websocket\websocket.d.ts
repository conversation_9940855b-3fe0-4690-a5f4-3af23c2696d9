import { AccountType } from './enums'

declare global {
  interface WSCallback {
    (...args: unknown[]): void
  }

  interface WSAccountBalance {
    balance: number
    isDemo: AccountType
  }

  interface WSAuthPayload {
    session: string
    isDemo: AccountType
    uid: number
    platform: number
    isFastHistory: boolean
  }

  interface WebSocketConfig {
    auth: WSAuthPayload
  }

  interface WSError {
    code: string
    message: string
    timestamp: number
    context?: unknown
  }

  interface WSConnectionMetrics {
    connectAttempts: number
    lastConnectTime: number | null
    lastDisconnectTime: number | null
    totalReconnects: number
    lastHeartbeatSent: number | null
    lastHeartbeatReceived: number | null
    averageLatency: number
    connectionUptime: number
  }

  interface WSHealthStatus {
    isHealthy: boolean
    latency: number | null
    uptime: number
    lastHeartbeat: number | null
    issues: string[]
  }

  interface WSPendingEventListener {
    event: string
    listener: (...args: unknown[]) => void
    once?: boolean
  }

  interface WSBufferedEvent {
    event: string
    data: unknown
    timestamp: number
    retryCount: number
  }

  interface WebSocketEvents {
    /** Connection established */
    connect: () => void
    /** Connection lost */
    disconnect: (reason: string) => void
    /** Connection error */
    error: (error: Error) => void
    /** Reconnection attempt */
    reconnect: (attemptNumber: number) => void
    /** Reconnection failed */
    reconnect_failed: () => void
    /** Custom message received */
    message: (data: unknown) => void
    /** Balance update received */
    successupdateBalance: (data: WSAccountBalance) => void
    /** Assets update received */
    updateAssets: (data: WSChartSettings) => void
    /** Health status update */
    'ws:health': (status: WSHealthStatus) => void
    /** Error notification */
    'ws:error': (error: WSError) => void
    /** Log message */
    'ws:log': (log: {
      level: string
      message: string
      context?: unknown
      timestamp: string
    }) => void
  }

  interface WSChartSettings {
    chartId: string
    chartType: number
    chartPeriod: number
    candlesTimer: boolean
    symbol: string
    demoDealAmount: number
    liveDealAmount: number
    enabledTradeMonitor: boolean
    enabledRatingWidget: boolean
    isVisible: boolean
    fastTimeframe: number
    enabledAutoscroll: boolean
    enabledGridSnap: boolean
    minimizedTradePanel: boolean
    fastCloseAt: number
    enableQuickAutoOffset: boolean
    quickAutoOffsetValue: number
    showArea: boolean
    percentAmount: number
  }
}
